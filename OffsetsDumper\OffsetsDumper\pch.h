// pch.h: Advanced FiveM Dumper - Precompiled Header
// Professional Memory Analysis Tool

#ifndef PCH_H
#define PCH_H

// Windows API Headers
#include "framework.h"
#include <Windows.h>
#include <TlHelp32.h>
#include <Psapi.h>
// #include <DbgHelp.h> // Removed for compatibility

// Forward declarations to avoid conflicts with Windows headers
#ifndef _UNICODE_STRING_DEFINED
#define _UNICODE_STRING_DEFINED
typedef struct _UNICODE_STRING {
    USHORT Length;
    USHORT MaximumLength;
    P<PERSON><PERSON>  Buffer;
} UNICODE_STRING, *PUNICODE_STRING;
#endif

// Use existing Windows definitions where possible
typedef CRITICAL_SECTION RTL_CRITICAL_SECTION, *PRTL_CRITICAL_SECTION;
typedef ULONG GDI_HANDLE_BUFFER[60];

// Simplified structure definitions
typedef struct _SIMPLE_LDR_DATA_TABLE_ENTRY {
    LIST_ENTRY InLoadOrderLinks;
    LIST_ENTRY InMemoryOrderLinks;
    LIST_ENTRY InInitializationOrderLinks;
    PVOID DllBase;
    PVOID EntryPoint;
    ULONG SizeOfImage;
    UNICODE_STRING FullDllName;
    UNICODE_STRING BaseDllName;
    ULONG Flags;
    WORD LoadCount;
    WORD TlsIndex;
} SIMPLE_LDR_DATA_TABLE_ENTRY, *PSIMPLE_LDR_DATA_TABLE_ENTRY;

typedef struct _SIMPLE_PEB_LDR_DATA {
    ULONG Length;
    BOOLEAN Initialized;
    HANDLE SsHandle;
    LIST_ENTRY InLoadOrderModuleList;
    LIST_ENTRY InMemoryOrderModuleList;
    LIST_ENTRY InInitializationOrderModuleList;
    PVOID EntryInProgress;
    BOOLEAN ShutdownInProgress;
    HANDLE ShutdownThreadId;
} SIMPLE_PEB_LDR_DATA, *PSIMPLE_PEB_LDR_DATA;

// Use simplified names to avoid conflicts
#define LDR_DATA_TABLE_ENTRY SIMPLE_LDR_DATA_TABLE_ENTRY
#define PLDR_DATA_TABLE_ENTRY PSIMPLE_LDR_DATA_TABLE_ENTRY
#define PEB_LDR_DATA SIMPLE_PEB_LDR_DATA
#define PPEB_LDR_DATA PSIMPLE_PEB_LDR_DATA

// Standard C++ Headers
#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <iomanip>
#include <algorithm>
#include <regex>
#include <functional>

// Simplified PEB structure for our needs
typedef struct _SIMPLE_PEB {
    BOOLEAN InheritedAddressSpace;
    BOOLEAN ReadImageFileExecOptions;
    BOOLEAN BeingDebugged;
    BOOLEAN BitField;
    HANDLE Mutant;
    PVOID ImageBaseAddress;
    PSIMPLE_PEB_LDR_DATA Ldr;
    PVOID ProcessParameters;
    PVOID SubSystemData;
    PVOID ProcessHeap;
    PRTL_CRITICAL_SECTION FastPebLock;
    PVOID AtlThunkSListPtr;
    PVOID IFEOKey;
    ULONG CrossProcessFlags;
    PVOID KernelCallbackTable;
    ULONG SystemReserved[1];
    ULONG AtlThunkSListPtr32;
    PVOID ApiSetMap;
    ULONG TlsExpansionCounter;
    PVOID TlsBitmap;
    ULONG TlsBitmapBits[2];
    PVOID ReadOnlySharedMemoryBase;
    PVOID HotpatchInformation;
    PVOID* ReadOnlyStaticServerData;
    PVOID AnsiCodePageData;
    PVOID OemCodePageData;
    PVOID UnicodeCaseTableData;
    ULONG NumberOfProcessors;
    ULONG NtGlobalFlag;
    LARGE_INTEGER CriticalSectionTimeout;
    SIZE_T HeapSegmentReserve;
    SIZE_T HeapSegmentCommit;
    SIZE_T HeapDeCommitTotalFreeThreshold;
    SIZE_T HeapDeCommitFreeBlockThreshold;
    ULONG NumberOfHeaps;
    ULONG MaximumNumberOfHeaps;
    PVOID* ProcessHeaps;
    PVOID GdiSharedHandleTable;
    PVOID ProcessStarterHelper;
    ULONG GdiDCAttributeList;
    PRTL_CRITICAL_SECTION LoaderLock;
    ULONG OSMajorVersion;
    ULONG OSMinorVersion;
    USHORT OSBuildNumber;
    USHORT OSCSDVersion;
    ULONG OSPlatformId;
    ULONG ImageSubsystem;
    ULONG ImageSubsystemMajorVersion;
    ULONG ImageSubsystemMinorVersion;
    ULONG_PTR ImageProcessAffinityMask;
    GDI_HANDLE_BUFFER GdiHandleBuffer;
    PVOID PostProcessInitRoutine;
    PVOID TlsExpansionBitmap;
    ULONG TlsExpansionBitmapBits[32];
    ULONG SessionId;
    ULARGE_INTEGER AppCompatFlags;
    ULARGE_INTEGER AppCompatFlagsUser;
    PVOID pShimData;
    PVOID AppCompatInfo;
    UNICODE_STRING CSDVersion;
    PVOID ActivationContextData;
    PVOID ProcessAssemblyStorageMap;
    PVOID SystemDefaultActivationContextData;
    PVOID SystemAssemblyStorageMap;
    SIZE_T MinimumStackCommit;
    PVOID* FlsCallback;
    LIST_ENTRY FlsListHead;
    PVOID FlsBitmap;
    ULONG FlsBitmapBits[128]; // Fixed size instead of FLS_MAXIMUM_AVAILABLE
    ULONG FlsHighIndex;
    PVOID WerRegistrationData;
    PVOID WerShipAssertPtr;
    PVOID pContextData;
    PVOID pImageHeaderHash;
    ULONG TracingFlags;
    ULONGLONG CsrServerReadOnlySharedMemoryBase;
} SIMPLE_PEB, *PSIMPLE_PEB;

// Use simplified name to avoid conflicts
#define PEB SIMPLE_PEB
#define PPEB PSIMPLE_PEB

// Utility macros
#define CONTAINING_RECORD(address, type, field) ((type *)( \
                                                  (PCHAR)(address) - \
                                                  (ULONG_PTR)(&((type *)0)->field)))

// Link with required libraries
#pragma comment(lib, "psapi.lib")

// Intrinsic functions
extern "C" {
    unsigned __int64 __readgsqword(unsigned long Offset);
}
#pragma intrinsic(__readgsqword)

#endif //PCH_H
