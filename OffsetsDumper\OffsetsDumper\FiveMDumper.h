#pragma once

#include "pch.h"
#include "../../config.h"
#include <Windows.h>
#include <TlHelp32.h>
#include <Psapi.h>
#include <vector>
#include <string>
#include <unordered_map>
#include <memory>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <thread>
#include <mutex>
#include <atomic>

// Advanced pattern scanning and memory analysis
class PatternScanner {
public:
    static uintptr_t FindPattern(uintptr_t start, size_t size, const char* pattern, const char* mask);
    static uintptr_t FindPatternInModule(const std::string& moduleName, const char* pattern, const char* mask);
    static std::vector<uintptr_t> FindAllPatterns(uintptr_t start, size_t size, const char* pattern, const char* mask);
};

// Memory region information
struct MemoryRegion {
    uintptr_t baseAddress;
    size_t size;
    DWORD protection;
    std::string moduleName;
    bool isExecutable;
    bool isWritable;
};

// Function information structure
struct FunctionInfo {
    uintptr_t address;
    std::string name;
    std::string signature;
    size_t size;
    std::vector<uintptr_t> references;
};

// Class/Structure information
struct ClassInfo {
    std::string name;
    uintptr_t vtableAddress;
    std::vector<uintptr_t> methods;
    std::unordered_map<std::string, size_t> memberOffsets;
    size_t totalSize;
};

// Main FiveM Dumper class
class FiveMDumper {
private:
    HMODULE m_hModule;
    std::vector<MemoryRegion> m_memoryRegions;
    std::unordered_map<std::string, FunctionInfo> m_functions;
    std::unordered_map<std::string, ClassInfo> m_classes;
    std::unordered_map<std::string, uintptr_t> m_offsets;
    std::mutex m_dumpMutex;
    std::atomic<bool> m_isRunning;
    
    // Output streams
    std::ofstream m_offsetsFile;
    std::ofstream m_functionsFile;
    std::ofstream m_classesFile;
    std::ofstream m_logFile;

public:
    explicit FiveMDumper(HMODULE hModule);
    ~FiveMDumper();

    // Core functionality
    bool Initialize();
    void StartDumping();
    void Cleanup();

    // Memory analysis
    void ScanMemoryRegions();
    void AnalyzeExecutableRegions();
    void DumpModuleInformation();
    
    // Pattern-based analysis
    void FindKnownPatterns();
    void AnalyzeVTables();
    void FindStringReferences();
    
    // FiveM specific analysis
    void FindFiveMStructures();
    void AnalyzeScriptEngine();
    void FindNetworkStructures();
    void AnalyzeEntitySystem();
    void FindRenderingStructures();
    
    // Advanced techniques
    void PerformStaticAnalysis();
    void AnalyzeImportTable();
    void FindHiddenFunctions();
    void ReconstructClasses();
    
    // Output functions
    void WriteOffsetsToFile();
    void WriteFunctionsToFile();
    void WriteClassesToFile();
    void GenerateHeaderFile();
    void GenerateCheatEngineTable();
    
    // Utility functions
    bool IsValidAddress(uintptr_t address);
    std::string GetModuleName(uintptr_t address);
    std::string DisassembleInstruction(uintptr_t address);
    void LogMessage(const std::string& message);
    bool SafeMemoryRead(uintptr_t address, void* buffer, size_t size);
    bool IsValidMemoryRegion(uintptr_t address, size_t size);
    bool PerformMemoryCopy(uintptr_t address, void* buffer, size_t size);
    void ShowResultsInConsole();
    void FindBasicOffsets(uintptr_t baseAddr, size_t moduleSize);
    void FindConservativePatterns(uintptr_t baseAddr, size_t moduleSize);
    
private:
    // Internal analysis functions
    void AnalyzeFunction(uintptr_t address, const std::string& name = "");
    void AnalyzeClass(uintptr_t vtableAddress, const std::string& className = "");
    void FindCrossReferences(uintptr_t address);
    std::vector<uintptr_t> GetCallTargets(uintptr_t functionStart, size_t functionSize);
    
    // Pattern definitions for FiveM
    void InitializeFiveMPatterns();
    
    // Security and anti-detection
    void BypassAntiDebug();
    void HideFromPEB();
    void MaskMemoryAccess();
};

// Utility macros and constants
#define FIVEM_BASE_PATTERN "\x48\x8B\x05\x00\x00\x00\x00\x48\x85\xC0\x74\x00\x48\x8B\x48"
#define FIVEM_BASE_MASK "xxx????xxxx?xxx"

#define ENTITY_POOL_PATTERN "\x4C\x8B\x0D\x00\x00\x00\x00\x44\x8B\xC1\x49\x8B\x41"
#define ENTITY_POOL_MASK "xxx????xxxxxx"

#define SCRIPT_THREAD_PATTERN "\x48\x89\x5C\x24\x00\x57\x48\x83\xEC\x20\x48\x8B\xD9\x48\x8B\xFA"
#define SCRIPT_THREAD_MASK "xxxx?xxxxxxxxxxx"

// Common FiveM offsets (will be dynamically found)
namespace FiveMOffsets {
    extern uintptr_t WorldPtr;
    extern uintptr_t EntityPool;
    extern uintptr_t PedPool;
    extern uintptr_t VehiclePool;
    extern uintptr_t ObjectPool;
    extern uintptr_t ScriptThreads;
    extern uintptr_t NetworkManager;
    extern uintptr_t Renderer;
    extern uintptr_t Camera;
    extern uintptr_t LocalPlayer;
}
