/*
    FiveM Safe Injector - Versão Conservadora
    
    Esta versão prioriza estabilidade sobre funcionalidades avançadas
    para evitar crashes do FiveM durante a injeção.
*/

#include <Windows.h>
#include <TlHelp32.h>
#include <Psapi.h>
#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>

class SafeFiveMInjector {
private:
    std::vector<std::string> m_targetProcesses = {
        "FiveM.exe",
        "FiveM_GTAProcess.exe",
        "GTA5.exe"
    };
    
    DWORD m_targetPID = 0;
    std::string m_targetProcessName;
    
public:
    bool FindFiveMProcess() {
        std::cout << "[INFO] Searching for FiveM processes..." << std::endl;
        
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            std::cout << "[ERROR] Failed to create process snapshot" << std::endl;
            return false;
        }
        
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);
        
        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                
                for (const auto& target : m_targetProcesses) {
                    if (processName == target) {
                        m_targetPID = pe32.th32ProcessID;
                        m_targetProcessName = processName;
                        
                        std::cout << "[SUCCESS] Found " << processName 
                                  << " (PID: " << m_targetPID << ")" << std::endl;
                        
                        CloseHandle(hSnapshot);
                        return true;
                    }
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        
        CloseHandle(hSnapshot);
        std::cout << "[ERROR] No FiveM process found" << std::endl;
        return false;
    }
    
    bool WaitForProcessStability() {
        std::cout << "[INFO] Waiting for process stability..." << std::endl;
        
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, 
                                     FALSE, m_targetPID);
        if (!hProcess) {
            std::cout << "[ERROR] Failed to open process for monitoring" << std::endl;
            return false;
        }
        
        // Wait for process to stabilize (check CPU usage)
        for (int i = 0; i < 10; i++) {
            FILETIME createTime, exitTime, kernelTime, userTime;
            if (GetProcessTimes(hProcess, &createTime, &exitTime, &kernelTime, &userTime)) {
                std::cout << "[INFO] Process stability check " << (i + 1) << "/10" << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(2));
            } else {
                CloseHandle(hProcess);
                std::cout << "[ERROR] Process no longer accessible" << std::endl;
                return false;
            }
        }
        
        CloseHandle(hProcess);
        std::cout << "[SUCCESS] Process appears stable" << std::endl;
        return true;
    }
    
    bool InjectDLL() {
        std::string dllPath = "OffsetsDumper\\x64\\Release\\OffsetsDumper.dll";
        
        // Check if DLL exists
        DWORD fileAttrib = GetFileAttributesA(dllPath.c_str());
        if (fileAttrib == INVALID_FILE_ATTRIBUTES) {
            std::cout << "[ERROR] DLL not found: " << dllPath << std::endl;
            std::cout << "[INFO] Please compile the project first using build.bat" << std::endl;
            return false;
        }
        
        std::cout << "[INFO] Attempting safe DLL injection..." << std::endl;
        
        // Open target process with minimal required permissions
        HANDLE hProcess = OpenProcess(PROCESS_CREATE_THREAD | PROCESS_QUERY_INFORMATION | 
                                     PROCESS_VM_OPERATION | PROCESS_VM_WRITE | PROCESS_VM_READ,
                                     FALSE, m_targetPID);
        
        if (!hProcess) {
            std::cout << "[ERROR] Failed to open target process (Error: " 
                      << GetLastError() << ")" << std::endl;
            std::cout << "[INFO] Make sure you're running as Administrator" << std::endl;
            return false;
        }
        
        // Get full path to DLL
        char fullPath[MAX_PATH];
        if (!GetFullPathNameA(dllPath.c_str(), MAX_PATH, fullPath, nullptr)) {
            std::cout << "[ERROR] Failed to get full DLL path" << std::endl;
            CloseHandle(hProcess);
            return false;
        }
        
        std::cout << "[INFO] DLL path: " << fullPath << std::endl;
        
        // Allocate memory in target process
        size_t pathLength = strlen(fullPath) + 1;
        LPVOID pRemotePath = VirtualAllocEx(hProcess, nullptr, pathLength, 
                                           MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        
        if (!pRemotePath) {
            std::cout << "[ERROR] Failed to allocate memory in target process" << std::endl;
            CloseHandle(hProcess);
            return false;
        }
        
        // Write DLL path to target process
        SIZE_T bytesWritten;
        if (!WriteProcessMemory(hProcess, pRemotePath, fullPath, pathLength, &bytesWritten)) {
            std::cout << "[ERROR] Failed to write DLL path to target process" << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Get LoadLibraryA address
        HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
        if (!hKernel32) {
            std::cout << "[ERROR] Failed to get kernel32.dll handle" << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        LPTHREAD_START_ROUTINE pLoadLibrary = 
            reinterpret_cast<LPTHREAD_START_ROUTINE>(GetProcAddress(hKernel32, "LoadLibraryA"));
        
        if (!pLoadLibrary) {
            std::cout << "[ERROR] Failed to get LoadLibraryA address" << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        std::cout << "[INFO] Creating remote thread..." << std::endl;
        
        // Create remote thread to load DLL
        HANDLE hThread = CreateRemoteThread(hProcess, nullptr, 0, pLoadLibrary, 
                                           pRemotePath, 0, nullptr);
        
        if (!hThread) {
            std::cout << "[ERROR] Failed to create remote thread (Error: " 
                      << GetLastError() << ")" << std::endl;
            VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        std::cout << "[INFO] Waiting for injection to complete..." << std::endl;
        
        // Wait for thread completion with timeout
        DWORD waitResult = WaitForSingleObject(hThread, 30000); // 30 second timeout
        
        if (waitResult == WAIT_TIMEOUT) {
            std::cout << "[WARNING] Injection thread timed out" << std::endl;
            TerminateThread(hThread, 0);
        } else if (waitResult == WAIT_OBJECT_0) {
            DWORD exitCode;
            if (GetExitCodeThread(hThread, &exitCode)) {
                if (exitCode != 0) {
                    std::cout << "[SUCCESS] DLL injected successfully!" << std::endl;
                } else {
                    std::cout << "[ERROR] DLL injection failed (LoadLibrary returned 0)" << std::endl;
                }
            }
        } else {
            std::cout << "[ERROR] Unexpected wait result: " << waitResult << std::endl;
        }
        
        // Cleanup
        CloseHandle(hThread);
        VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        
        return waitResult == WAIT_OBJECT_0;
    }
    
    void ShowInstructions() {
        std::cout << "\n========================================" << std::endl;
        std::cout << "    FiveM Safe Dumper Instructions" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << "\n1. The dumper is now running inside FiveM" << std::endl;
        std::cout << "2. Wait 2-5 minutes for analysis to complete" << std::endl;
        std::cout << "3. Check FiveMDumps\\ folder for results:" << std::endl;
        std::cout << "   - offsets.txt (Main offsets)" << std::endl;
        std::cout << "   - FiveMOffsets.h (C++ header)" << std::endl;
        std::cout << "   - log.txt (Detailed log)" << std::endl;
        std::cout << "\n4. If FiveM becomes unstable, restart it" << std::endl;
        std::cout << "5. The dumper will auto-cleanup when done" << std::endl;
        std::cout << "\n========================================" << std::endl;
    }
};

int main() {
    std::cout << "========================================" << std::endl;
    std::cout << "    FiveM Advanced Dumper - Safe Mode" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "Version: 2.0 Safe Edition" << std::endl;
    std::cout << "Focus: Stability over advanced features" << std::endl;
    std::cout << "========================================\n" << std::endl;
    
    SafeFiveMInjector injector;
    
    // Step 1: Find FiveM process
    if (!injector.FindFiveMProcess()) {
        std::cout << "\n[INFO] Please:" << std::endl;
        std::cout << "1. Start FiveM" << std::endl;
        std::cout << "2. Join any server" << std::endl;
        std::cout << "3. Wait for the game to load completely" << std::endl;
        std::cout << "4. Run this injector again" << std::endl;
        
        std::cout << "\nPress any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    // Step 2: Wait for process stability
    if (!injector.WaitForProcessStability()) {
        std::cout << "[ERROR] Process is not stable enough for injection" << std::endl;
        std::cout << "[INFO] Please wait longer and try again" << std::endl;
        
        std::cout << "\nPress any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    // Step 3: Inject DLL
    if (injector.InjectDLL()) {
        injector.ShowInstructions();
    } else {
        std::cout << "\n[ERROR] Injection failed!" << std::endl;
        std::cout << "[INFO] Troubleshooting:" << std::endl;
        std::cout << "1. Run as Administrator" << std::endl;
        std::cout << "2. Disable antivirus temporarily" << std::endl;
        std::cout << "3. Make sure DLL was compiled successfully" << std::endl;
        std::cout << "4. Check if FiveM is still running" << std::endl;
    }
    
    std::cout << "\nPress any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}
