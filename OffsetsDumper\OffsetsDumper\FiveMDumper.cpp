#include "pch.h"
#include "FiveMDumper.h"
#include <algorithm>
#include <regex>

// Initialize FiveM offsets namespace
namespace FiveMOffsets {
    uintptr_t WorldPtr = 0;
    uintptr_t EntityPool = 0;
    uintptr_t PedPool = 0;
    uintptr_t VehiclePool = 0;
    uintptr_t ObjectPool = 0;
    uintptr_t ScriptThreads = 0;
    uintptr_t NetworkManager = 0;
    uintptr_t Renderer = 0;
    uintptr_t Camera = 0;
    uintptr_t LocalPlayer = 0;
}

// PatternScanner implementation
uintptr_t PatternScanner::FindPattern(uintptr_t start, size_t size, const char* pattern, const char* mask) {
    size_t patternLength = strlen(mask);

    // Safety check
    if (patternLength == 0 || size < patternLength) return 0;

    for (size_t i = 0; i <= size - patternLength; i++) {
        bool found = true;

        // Safe memory access check
        __try {
            for (size_t j = 0; j < patternLength; j++) {
                if (mask[j] != '?') {
                    char memByte = *reinterpret_cast<volatile char*>(start + i + j);
                    if (pattern[j] != memByte) {
                        found = false;
                        break;
                    }
                }
            }
        }
        __except (EXCEPTION_EXECUTE_HANDLER) {
            // Memory access violation - skip this location
            found = false;
        }

        if (found) {
            return start + i;
        }
    }
    return 0;
}

uintptr_t PatternScanner::FindPatternInModule(const std::string& moduleName, const char* pattern, const char* mask) {
    HMODULE hModule = GetModuleHandleA(moduleName.c_str());
    if (!hModule) return 0;
    
    MODULEINFO modInfo;
    if (!GetModuleInformation(GetCurrentProcess(), hModule, &modInfo, sizeof(modInfo))) {
        return 0;
    }
    
    return FindPattern(reinterpret_cast<uintptr_t>(hModule), modInfo.SizeOfImage, pattern, mask);
}

std::vector<uintptr_t> PatternScanner::FindAllPatterns(uintptr_t start, size_t size, const char* pattern, const char* mask) {
    std::vector<uintptr_t> results;
    size_t patternLength = strlen(mask);
    
    for (size_t i = 0; i <= size - patternLength; i++) {
        bool found = true;
        for (size_t j = 0; j < patternLength; j++) {
            if (mask[j] != '?' && pattern[j] != *(char*)(start + i + j)) {
                found = false;
                break;
            }
        }
        if (found) {
            results.push_back(start + i);
            i += patternLength - 1; // Skip ahead to avoid overlapping matches
        }
    }
    return results;
}

// FiveMDumper implementation
FiveMDumper::FiveMDumper(HMODULE hModule) 
    : m_hModule(hModule), m_isRunning(false) {
}

FiveMDumper::~FiveMDumper() {
    Cleanup();
}

bool FiveMDumper::Initialize() {
    try {
        // Create output directory
        CreateDirectoryA("FiveMDumps", nullptr);
        
        // Open output files
        m_offsetsFile.open("FiveMDumps/offsets.txt");
        m_functionsFile.open("FiveMDumps/functions.txt");
        m_classesFile.open("FiveMDumps/classes.txt");
        m_logFile.open("FiveMDumps/log.txt");
        
        if (!m_offsetsFile.is_open() || !m_functionsFile.is_open() || 
            !m_classesFile.is_open() || !m_logFile.is_open()) {
            LogMessage("Failed to open output files");
            return false;
        }
        
        LogMessage("FiveM Dumper initialized successfully");
        
        // Apply anti-detection measures
        BypassAntiDebug();
        HideFromPEB();
        
        return true;
    }
    catch (const std::exception& e) {
        LogMessage("Initialization failed: " + std::string(e.what()));
        return false;
    }
}

void FiveMDumper::StartDumping() {
    if (m_isRunning.exchange(true)) {
        LogMessage("Dumper already running");
        return;
    }
    
    LogMessage("Starting FiveM memory analysis...");
    
    try {
        // Phase 1: Basic memory analysis
        ScanMemoryRegions();
        DumpModuleInformation();
        
        // Phase 2: Pattern-based analysis
        FindKnownPatterns();
        AnalyzeVTables();
        FindStringReferences();
        
        // Phase 3: FiveM specific analysis
        FindFiveMStructures();
        AnalyzeScriptEngine();
        FindNetworkStructures();
        AnalyzeEntitySystem();
        FindRenderingStructures();
        
        // Phase 4: Advanced analysis
        PerformStaticAnalysis();
        AnalyzeImportTable();
        FindHiddenFunctions();
        ReconstructClasses();
        
        // Phase 5: Output generation
        WriteOffsetsToFile();
        WriteFunctionsToFile();
        WriteClassesToFile();
        GenerateHeaderFile();
        GenerateCheatEngineTable();
        
        LogMessage("Dumping completed successfully!");
        MessageBoxA(nullptr, "FiveM dumping completed! Check FiveMDumps folder for results.", 
                   "Success", MB_ICONINFORMATION);
    }
    catch (const std::exception& e) {
        LogMessage("Dumping failed: " + std::string(e.what()));
        MessageBoxA(nullptr, ("Dumping failed: " + std::string(e.what())).c_str(), 
                   "Error", MB_ICONERROR);
    }
    
    m_isRunning = false;
}

void FiveMDumper::Cleanup() {
    m_isRunning = false;
    
    if (m_offsetsFile.is_open()) m_offsetsFile.close();
    if (m_functionsFile.is_open()) m_functionsFile.close();
    if (m_classesFile.is_open()) m_classesFile.close();
    if (m_logFile.is_open()) m_logFile.close();
    
    LogMessage("FiveM Dumper cleanup completed");
}

void FiveMDumper::ScanMemoryRegions() {
    LogMessage("Scanning memory regions...");
    
    MEMORY_BASIC_INFORMATION mbi;
    uintptr_t address = 0;
    
    while (VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi))) {
        if (mbi.State == MEM_COMMIT && mbi.Protect != PAGE_NOACCESS) {
            MemoryRegion region;
            region.baseAddress = reinterpret_cast<uintptr_t>(mbi.BaseAddress);
            region.size = mbi.RegionSize;
            region.protection = mbi.Protect;
            region.isExecutable = (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE)) != 0;
            region.isWritable = (mbi.Protect & (PAGE_READWRITE | PAGE_EXECUTE_READWRITE | PAGE_WRITECOPY)) != 0;
            
            // Get module name
            char moduleName[MAX_PATH];
            if (GetModuleFileNameA(reinterpret_cast<HMODULE>(region.baseAddress), moduleName, MAX_PATH)) {
                region.moduleName = moduleName;
            }
            
            m_memoryRegions.push_back(region);
        }
        
        address = reinterpret_cast<uintptr_t>(mbi.BaseAddress) + mbi.RegionSize;
    }
    
    LogMessage("Found " + std::to_string(m_memoryRegions.size()) + " memory regions");
}

void FiveMDumper::FindKnownPatterns() {
    LogMessage("Searching for known FiveM patterns...");
    
    // Find World Pointer
    uintptr_t worldPattern = PatternScanner::FindPatternInModule("", FIVEM_BASE_PATTERN, FIVEM_BASE_MASK);
    if (worldPattern) {
        FiveMOffsets::WorldPtr = worldPattern;
        m_offsets["WorldPtr"] = worldPattern;
        LogMessage("Found WorldPtr at: 0x" + std::to_string(worldPattern));
    }
    
    // Find Entity Pool
    uintptr_t entityPattern = PatternScanner::FindPatternInModule("", ENTITY_POOL_PATTERN, ENTITY_POOL_MASK);
    if (entityPattern) {
        FiveMOffsets::EntityPool = entityPattern;
        m_offsets["EntityPool"] = entityPattern;
        LogMessage("Found EntityPool at: 0x" + std::to_string(entityPattern));
    }
    
    // Find Script Threads
    uintptr_t scriptPattern = PatternScanner::FindPatternInModule("", SCRIPT_THREAD_PATTERN, SCRIPT_THREAD_MASK);
    if (scriptPattern) {
        FiveMOffsets::ScriptThreads = scriptPattern;
        m_offsets["ScriptThreads"] = scriptPattern;
        LogMessage("Found ScriptThreads at: 0x" + std::to_string(scriptPattern));
    }
}

bool FiveMDumper::IsValidAddress(uintptr_t address) {
    if (address == 0) return false;
    
    MEMORY_BASIC_INFORMATION mbi;
    if (!VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi))) {
        return false;
    }
    
    return (mbi.State == MEM_COMMIT && mbi.Protect != PAGE_NOACCESS);
}

void FiveMDumper::LogMessage(const std::string& message) {
    std::lock_guard<std::mutex> lock(m_dumpMutex);
    
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    struct tm tm;
    localtime_s(&tm, &time_t);

    if (m_logFile.is_open()) {
        m_logFile << "[" << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << "] "
                  << message << std::endl;
        m_logFile.flush();
    }
}

// Anti-detection measures
void FiveMDumper::BypassAntiDebug() {
    // Clear debug flags in PEB
    try {
        PPEB peb = reinterpret_cast<PPEB>(__readgsqword(0x60));
        if (peb) {
            peb->BeingDebugged = FALSE;
            peb->NtGlobalFlag &= ~0x70;
        }
    }
    catch (...) {
        // Ignore exceptions
        LogMessage("Failed to bypass anti-debug");
    }
}

void FiveMDumper::HideFromPEB() {
    // Hide module from PEB module list
    try {
        PPEB peb = reinterpret_cast<PPEB>(__readgsqword(0x60));
        if (!peb || !peb->Ldr) return;

        PLIST_ENTRY head = &peb->Ldr->InMemoryOrderModuleList;
        PLIST_ENTRY current = head->Flink;

        while (current && current != head) {
            PLDR_DATA_TABLE_ENTRY entry = CONTAINING_RECORD(current, LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);
            if (entry && entry->DllBase == m_hModule) {
                // Unlink from all lists
                if (entry->InLoadOrderLinks.Flink && entry->InLoadOrderLinks.Blink) {
                    entry->InLoadOrderLinks.Flink->Blink = entry->InLoadOrderLinks.Blink;
                    entry->InLoadOrderLinks.Blink->Flink = entry->InLoadOrderLinks.Flink;
                }
                if (entry->InMemoryOrderLinks.Flink && entry->InMemoryOrderLinks.Blink) {
                    entry->InMemoryOrderLinks.Flink->Blink = entry->InMemoryOrderLinks.Blink;
                    entry->InMemoryOrderLinks.Blink->Flink = entry->InMemoryOrderLinks.Flink;
                }
                if (entry->InInitializationOrderLinks.Flink && entry->InInitializationOrderLinks.Blink) {
                    entry->InInitializationOrderLinks.Flink->Blink = entry->InInitializationOrderLinks.Blink;
                    entry->InInitializationOrderLinks.Blink->Flink = entry->InInitializationOrderLinks.Flink;
                }
                break;
            }
            current = current->Flink;
        }
    }
    catch (...) {
        // Ignore exceptions
        LogMessage("Failed to hide from PEB");
    }
}

void FiveMDumper::MaskMemoryAccess() {
    // Implement memory access masking techniques
    LogMessage("Applying memory access masking...");
}

bool FiveMDumper::SafeMemoryRead(uintptr_t address, void* buffer, size_t size) {
    if (!buffer || size == 0) return false;

    __try {
        // Check if the entire region is valid
        if (!IsValidMemoryRegion(address, size)) {
            return false;
        }

        // Perform the read
        memcpy(buffer, reinterpret_cast<const void*>(address), size);
        return true;
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        // Memory access violation
        LogMessage("Memory access violation at address: 0x" +
                  std::to_string(address) + " size: " + std::to_string(size));
        return false;
    }
}

bool FiveMDumper::IsValidMemoryRegion(uintptr_t address, size_t size) {
    if (address == 0 || size == 0) return false;

    // Check for overflow
    if (address + size < address) return false;

    // Check reasonable address range (avoid kernel space)
    if (address < 0x10000 || address > 0x7FFFFFFFFFFF) return false;

    __try {
        MEMORY_BASIC_INFORMATION mbi;
        uintptr_t currentAddr = address;
        uintptr_t endAddr = address + size;

        while (currentAddr < endAddr) {
            if (!VirtualQuery(reinterpret_cast<LPCVOID>(currentAddr), &mbi, sizeof(mbi))) {
                return false;
            }

            // Check if memory is committed and accessible
            if (mbi.State != MEM_COMMIT ||
                mbi.Protect == PAGE_NOACCESS ||
                mbi.Protect == PAGE_GUARD) {
                return false;
            }

            // Move to next region
            currentAddr = reinterpret_cast<uintptr_t>(mbi.BaseAddress) + mbi.RegionSize;
        }

        return true;
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        return false;
    }
}

void FiveMDumper::DumpModuleInformation() {
    LogMessage("Dumping module information...");

    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE, GetCurrentProcessId());
    if (hSnapshot == INVALID_HANDLE_VALUE) return;

    MODULEENTRY32 me32;
    me32.dwSize = sizeof(MODULEENTRY32);

    if (Module32First(hSnapshot, &me32)) {
        do {
            std::stringstream ss;
            ss << "Module: " << me32.szModule
               << " | Base: 0x" << std::hex << reinterpret_cast<uintptr_t>(me32.modBaseAddr)
               << " | Size: 0x" << me32.modBaseSize;
            LogMessage(ss.str());
        } while (Module32Next(hSnapshot, &me32));
    }

    CloseHandle(hSnapshot);
}

void FiveMDumper::AnalyzeExecutableRegions() {
    LogMessage("Analyzing executable regions...");

    for (const auto& region : m_memoryRegions) {
        if (region.isExecutable && region.size > 0x1000) {
            // Analyze this executable region
            LogMessage("Analyzing executable region at 0x" +
                      std::to_string(region.baseAddress) + " size: 0x" +
                      std::to_string(region.size));

            // Look for function prologues
            const char* functionPattern = "\x48\x89\x5C\x24\x00\x48\x89\x74\x24";
            const char* functionMask = "xxxx?xxxx";

            auto functions = PatternScanner::FindAllPatterns(
                region.baseAddress, region.size, functionPattern, functionMask);

            for (auto funcAddr : functions) {
                AnalyzeFunction(funcAddr);
            }
        }
    }
}

void FiveMDumper::AnalyzeVTables() {
    LogMessage("Analyzing virtual tables...");

    for (const auto& region : m_memoryRegions) {
        if (!region.isExecutable && region.isWritable && region.size > 0x1000) {
            // Look for potential vtables (arrays of function pointers)
            for (size_t i = 0; i < region.size - sizeof(uintptr_t) * 4; i += sizeof(uintptr_t)) {
                uintptr_t* potential_vtable = reinterpret_cast<uintptr_t*>(region.baseAddress + i);

                // Check if this looks like a vtable (multiple consecutive valid function pointers)
                int validPointers = 0;
                for (int j = 0; j < 8; j++) {
                    if (IsValidAddress(potential_vtable[j])) {
                        // Check if it points to executable memory
                        MEMORY_BASIC_INFORMATION mbi;
                        if (VirtualQuery(reinterpret_cast<LPCVOID>(potential_vtable[j]), &mbi, sizeof(mbi))) {
                            if (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE)) {
                                validPointers++;
                            }
                        }
                    } else {
                        break;
                    }
                }

                if (validPointers >= 3) {
                    // This looks like a vtable
                    std::stringstream ss;
                    ss << "Potential VTable at 0x" << std::hex << (region.baseAddress + i)
                       << " with " << validPointers << " methods";
                    LogMessage(ss.str());

                    AnalyzeClass(region.baseAddress + i);
                }
            }
        }
    }
}

void FiveMDumper::FindStringReferences() {
    LogMessage("Finding string references...");

    // Look for interesting strings that might indicate important structures
    std::vector<std::string> interestingStrings = {
        "CWorld", "CPed", "CVehicle", "CObject", "CEntity",
        "ScriptThread", "NetworkManager", "Renderer", "Camera",
        "Player", "Weapon", "Blip", "Pickup", "Checkpoint"
    };

    for (const auto& region : m_memoryRegions) {
        if (!region.isExecutable) {
            for (const auto& searchStr : interestingStrings) {
                // Simple string search
                for (size_t i = 0; i <= region.size - searchStr.length(); i++) {
                    if (memcmp(reinterpret_cast<void*>(region.baseAddress + i),
                              searchStr.c_str(), searchStr.length()) == 0) {

                        std::stringstream ss;
                        ss << "Found string '" << searchStr << "' at 0x"
                           << std::hex << (region.baseAddress + i);
                        LogMessage(ss.str());

                        // Look for references to this string
                        FindCrossReferences(region.baseAddress + i);
                    }
                }
            }
        }
    }
}

void FiveMDumper::FindFiveMStructures() {
    LogMessage("Searching for FiveM-specific structures...");

    // Advanced pattern scanning for FiveM structures
    struct PatternInfo {
        const char* name;
        const char* pattern;
        const char* mask;
    };

    std::vector<PatternInfo> fivemPatterns = {
        {"PedFactory", "\x48\x8B\x05\x00\x00\x00\x00\x48\x8B\x50\x08\x48\x85\xD2", "xxx????xxxxxxx"},
        {"VehicleFactory", "\x48\x8B\x05\x00\x00\x00\x00\x48\x8B\x50\x10\x48\x85\xD2", "xxx????xxxxxxx"},
        {"ObjectFactory", "\x48\x8B\x05\x00\x00\x00\x00\x48\x8B\x50\x18\x48\x85\xD2", "xxx????xxxxxxx"},
        {"BlipList", "\x4C\x8B\x0D\x00\x00\x00\x00\x49\x8B\x41\x08", "xxx????xxxx"},
        {"WeaponManager", "\x48\x8B\x0D\x00\x00\x00\x00\x48\x85\xC9\x0F\x84", "xxx????xxxxx"},
        {"NetworkPlayerMgr", "\x48\x8B\x0D\x00\x00\x00\x00\x8A\xD3\x48\x8B\x01", "xxx????xxxxx"}
    };

    for (const auto& patternInfo : fivemPatterns) {
        uintptr_t found = PatternScanner::FindPatternInModule("", patternInfo.pattern, patternInfo.mask);
        if (found) {
            m_offsets[patternInfo.name] = found;
            LogMessage("Found " + std::string(patternInfo.name) + " at: 0x" +
                      std::to_string(found));
        }
    }
}

void FiveMDumper::AnalyzeScriptEngine() {
    LogMessage("Analyzing script engine...");

    // Look for script thread structures
    const char* scriptPattern = "\x48\x8B\xCB\x48\x8B\x01\xFF\x50\x00\x84\xC0\x75";
    const char* scriptMask = "xxxxxxxx?xxx";

    auto scriptRefs = PatternScanner::FindAllPatterns(
        reinterpret_cast<uintptr_t>(GetModuleHandle(nullptr)),
        0x10000000, scriptPattern, scriptMask);

    for (auto ref : scriptRefs) {
        LogMessage("Script engine reference at: 0x" + std::to_string(ref));
    }
}

void FiveMDumper::FindNetworkStructures() {
    LogMessage("Finding network structures...");

    // Network-related patterns
    const char* netPattern = "\x48\x8B\x0D\x00\x00\x00\x00\x48\x85\xC9\x74\x00\x48\x8B\x01\xFF\x90";
    const char* netMask = "xxx????xxxx?xxxxx";

    uintptr_t netMgr = PatternScanner::FindPatternInModule("", netPattern, netMask);
    if (netMgr) {
        FiveMOffsets::NetworkManager = netMgr;
        m_offsets["NetworkManager"] = netMgr;
        LogMessage("Found NetworkManager at: 0x" + std::to_string(netMgr));
    }
}

void FiveMDumper::AnalyzeEntitySystem() {
    LogMessage("Analyzing entity system...");

    // Entity pool patterns
    std::vector<std::pair<std::string, std::pair<const char*, const char*>>> entityPatterns = {
        {"PedPool", {"\x48\x8B\x05\x00\x00\x00\x00\x41\x0F\xBF\xC8\x48\x8B\x50\x08", "xxx????xxxxxxxx"}},
        {"VehiclePool", {"\x48\x8B\x05\x00\x00\x00\x00\x41\x0F\xBF\xC8\x48\x8B\x50\x10", "xxx????xxxxxxxx"}},
        {"ObjectPool", {"\x48\x8B\x05\x00\x00\x00\x00\x41\x0F\xBF\xC8\x48\x8B\x50\x18", "xxx????xxxxxxxx"}}
    };

    for (const auto& pattern : entityPatterns) {
        uintptr_t found = PatternScanner::FindPatternInModule("", pattern.second.first, pattern.second.second);
        if (found) {
            m_offsets[pattern.first] = found;
            LogMessage("Found " + pattern.first + " at: 0x" + std::to_string(found));
        }
    }
}

void FiveMDumper::FindRenderingStructures() {
    LogMessage("Finding rendering structures...");

    // Renderer and camera patterns
    const char* rendererPattern = "\x48\x8B\x0D\x00\x00\x00\x00\x48\x85\xC9\x74\x00\x48\x8B\x01\xFF\x50\x00\x48\x8B\xC8";
    const char* rendererMask = "xxx????xxxx?xxxxx?xxx";

    uintptr_t renderer = PatternScanner::FindPatternInModule("", rendererPattern, rendererMask);
    if (renderer) {
        FiveMOffsets::Renderer = renderer;
        m_offsets["Renderer"] = renderer;
        LogMessage("Found Renderer at: 0x" + std::to_string(renderer));
    }

    const char* cameraPattern = "\x48\x8B\x05\x00\x00\x00\x00\x48\x85\xC0\x74\x00\xF3\x0F\x10\x40";
    const char* cameraMask = "xxx????xxxx?xxxx";

    uintptr_t camera = PatternScanner::FindPatternInModule("", cameraPattern, cameraMask);
    if (camera) {
        FiveMOffsets::Camera = camera;
        m_offsets["Camera"] = camera;
        LogMessage("Found Camera at: 0x" + std::to_string(camera));
    }
}

void FiveMDumper::PerformStaticAnalysis() {
    LogMessage("Performing static analysis...");

    // Analyze PE headers and sections
    HMODULE hMain = GetModuleHandle(nullptr);
    if (!hMain) return;

    IMAGE_DOS_HEADER* dosHeader = reinterpret_cast<IMAGE_DOS_HEADER*>(hMain);
    if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) return;

    IMAGE_NT_HEADERS* ntHeaders = reinterpret_cast<IMAGE_NT_HEADERS*>(
        reinterpret_cast<uintptr_t>(hMain) + dosHeader->e_lfanew);

    if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) return;

    // Analyze sections
    IMAGE_SECTION_HEADER* sections = IMAGE_FIRST_SECTION(ntHeaders);
    for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; i++) {
        std::stringstream ss;
        ss << "Section: " << std::string(reinterpret_cast<char*>(sections[i].Name), 8)
           << " | RVA: 0x" << std::hex << sections[i].VirtualAddress
           << " | Size: 0x" << sections[i].Misc.VirtualSize
           << " | Characteristics: 0x" << sections[i].Characteristics;
        LogMessage(ss.str());
    }
}

void FiveMDumper::AnalyzeImportTable() {
    LogMessage("Analyzing import table...");

    HMODULE hMain = GetModuleHandle(nullptr);
    if (!hMain) return;

    IMAGE_DOS_HEADER* dosHeader = reinterpret_cast<IMAGE_DOS_HEADER*>(hMain);
    IMAGE_NT_HEADERS* ntHeaders = reinterpret_cast<IMAGE_NT_HEADERS*>(
        reinterpret_cast<uintptr_t>(hMain) + dosHeader->e_lfanew);

    IMAGE_DATA_DIRECTORY* importDir = &ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT];
    if (importDir->VirtualAddress == 0) return;

    IMAGE_IMPORT_DESCRIPTOR* importDesc = reinterpret_cast<IMAGE_IMPORT_DESCRIPTOR*>(
        reinterpret_cast<uintptr_t>(hMain) + importDir->VirtualAddress);

    while (importDesc->Name != 0) {
        char* moduleName = reinterpret_cast<char*>(reinterpret_cast<uintptr_t>(hMain) + importDesc->Name);
        LogMessage("Import module: " + std::string(moduleName));

        IMAGE_THUNK_DATA* thunk = reinterpret_cast<IMAGE_THUNK_DATA*>(
            reinterpret_cast<uintptr_t>(hMain) + importDesc->FirstThunk);

        while (thunk->u1.AddressOfData != 0) {
            if (!(thunk->u1.Ordinal & IMAGE_ORDINAL_FLAG)) {
                IMAGE_IMPORT_BY_NAME* importByName = reinterpret_cast<IMAGE_IMPORT_BY_NAME*>(
                    reinterpret_cast<uintptr_t>(hMain) + thunk->u1.AddressOfData);

                std::stringstream ss;
                ss << "  Function: " << importByName->Name
                   << " | Address: 0x" << std::hex << reinterpret_cast<uintptr_t>(&thunk->u1.Function);
                LogMessage(ss.str());
            }
            thunk++;
        }
        importDesc++;
    }
}

void FiveMDumper::FindHiddenFunctions() {
    LogMessage("Finding hidden functions...");

    // Look for functions that might be hidden or obfuscated
    for (const auto& region : m_memoryRegions) {
        if (region.isExecutable) {
            // Look for unusual function patterns
            const char* hiddenPattern = "\xE8\x00\x00\x00\x00\x58\x48\x83\xC0"; // call $+5; pop rax; add rax, X
            const char* hiddenMask = "x????xxxx";

            auto hiddenFuncs = PatternScanner::FindAllPatterns(
                region.baseAddress, region.size, hiddenPattern, hiddenMask);

            for (auto func : hiddenFuncs) {
                LogMessage("Potential hidden function at: 0x" + std::to_string(func));
                AnalyzeFunction(func, "HiddenFunction");
            }
        }
    }
}

void FiveMDumper::ReconstructClasses() {
    LogMessage("Reconstructing classes...");

    // Analyze found vtables to reconstruct class structures
    for (const auto& classInfo : m_classes) {
        LogMessage("Reconstructing class: " + classInfo.first);

        // Analyze virtual methods
        for (size_t i = 0; i < classInfo.second.methods.size(); i++) {
            uintptr_t methodAddr = classInfo.second.methods[i];
            if (IsValidAddress(methodAddr)) {
                std::string methodName = classInfo.first + "::vfunc_" + std::to_string(i);
                AnalyzeFunction(methodAddr, methodName);
            }
        }
    }
}

void FiveMDumper::WriteOffsetsToFile() {
    LogMessage("Writing offsets to file...");

    if (!m_offsetsFile.is_open()) return;

    m_offsetsFile << "// FiveM Offsets - Generated by Advanced Dumper\n";
    m_offsetsFile << "// Timestamp: " << std::time(nullptr) << "\n\n";

    m_offsetsFile << "#pragma once\n\n";
    m_offsetsFile << "namespace FiveMOffsets {\n";

    for (const auto& offset : m_offsets) {
        m_offsetsFile << "    constexpr uintptr_t " << offset.first
                     << " = 0x" << std::hex << offset.second << ";\n";
    }

    m_offsetsFile << "}\n\n";

    // Write detailed offset information
    m_offsetsFile << "// Detailed Offset Information:\n";
    for (const auto& offset : m_offsets) {
        m_offsetsFile << "// " << offset.first << ": 0x" << std::hex << offset.second;

        // Try to get module information
        std::string moduleName = GetModuleName(offset.second);
        if (!moduleName.empty()) {
            m_offsetsFile << " (Module: " << moduleName << ")";
        }
        m_offsetsFile << "\n";
    }

    m_offsetsFile.flush();
}

void FiveMDumper::WriteFunctionsToFile() {
    LogMessage("Writing functions to file...");

    if (!m_functionsFile.is_open()) return;

    m_functionsFile << "// FiveM Functions - Generated by Advanced Dumper\n";
    m_functionsFile << "// Total Functions Found: " << m_functions.size() << "\n\n";

    for (const auto& func : m_functions) {
        m_functionsFile << "Function: " << func.first << "\n";
        m_functionsFile << "  Address: 0x" << std::hex << func.second.address << "\n";
        m_functionsFile << "  Size: 0x" << func.second.size << "\n";
        m_functionsFile << "  Signature: " << func.second.signature << "\n";
        m_functionsFile << "  References: " << func.second.references.size() << "\n";

        for (const auto& ref : func.second.references) {
            m_functionsFile << "    0x" << std::hex << ref << "\n";
        }
        m_functionsFile << "\n";
    }

    m_functionsFile.flush();
}

void FiveMDumper::WriteClassesToFile() {
    LogMessage("Writing classes to file...");

    if (!m_classesFile.is_open()) return;

    m_classesFile << "// FiveM Classes - Generated by Advanced Dumper\n";
    m_classesFile << "// Total Classes Found: " << m_classes.size() << "\n\n";

    for (const auto& cls : m_classes) {
        m_classesFile << "Class: " << cls.first << "\n";
        m_classesFile << "  VTable: 0x" << std::hex << cls.second.vtableAddress << "\n";
        m_classesFile << "  Size: 0x" << cls.second.totalSize << "\n";
        m_classesFile << "  Methods: " << cls.second.methods.size() << "\n";

        for (size_t i = 0; i < cls.second.methods.size(); i++) {
            m_classesFile << "    [" << i << "] 0x" << std::hex << cls.second.methods[i] << "\n";
        }

        m_classesFile << "  Members:\n";
        for (const auto& member : cls.second.memberOffsets) {
            m_classesFile << "    " << member.first << ": +0x" << std::hex << member.second << "\n";
        }
        m_classesFile << "\n";
    }

    m_classesFile.flush();
}

void FiveMDumper::GenerateHeaderFile() {
    LogMessage("Generating header file...");

    std::ofstream headerFile("FiveMDumps/FiveMOffsets.h");
    if (!headerFile.is_open()) return;

    headerFile << "#pragma once\n";
    headerFile << "#include <cstdint>\n\n";
    headerFile << "// FiveM Offsets and Structures\n";
    headerFile << "// Generated by Advanced FiveM Dumper\n\n";

    // Write offsets
    headerFile << "namespace Offsets {\n";
    for (const auto& offset : m_offsets) {
        headerFile << "    constexpr uintptr_t " << offset.first
                  << " = 0x" << std::hex << offset.second << ";\n";
    }
    headerFile << "}\n\n";

    // Write class definitions
    headerFile << "// Class Definitions\n";
    for (const auto& cls : m_classes) {
        headerFile << "class " << cls.first << " {\n";
        headerFile << "public:\n";
        headerFile << "    static constexpr uintptr_t VTable = 0x"
                  << std::hex << cls.second.vtableAddress << ";\n";

        for (const auto& member : cls.second.memberOffsets) {
            headerFile << "    // " << member.first << " at +0x"
                      << std::hex << member.second << "\n";
        }

        headerFile << "};\n\n";
    }

    headerFile.close();
}

void FiveMDumper::GenerateCheatEngineTable() {
    LogMessage("Generating Cheat Engine table...");

    std::ofstream ceTable("FiveMDumps/FiveM.CT");
    if (!ceTable.is_open()) return;

    ceTable << "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n";
    ceTable << "<CheatTable>\n";
    ceTable << "  <CheatEntries>\n";

    for (const auto& offset : m_offsets) {
        ceTable << "    <CheatEntry>\n";
        ceTable << "      <ID>" << std::hash<std::string>{}(offset.first) << "</ID>\n";
        ceTable << "      <Description>\"" << offset.first << "\"</Description>\n";
        ceTable << "      <LastState Value=\"\" RealAddress=\"" << std::hex << offset.second << "\"/>\n";
        ceTable << "      <VariableType>8 Bytes</VariableType>\n";
        ceTable << "      <Address>" << std::hex << offset.second << "</Address>\n";
        ceTable << "    </CheatEntry>\n";
    }

    ceTable << "  </CheatEntries>\n";
    ceTable << "</CheatTable>\n";

    ceTable.close();
}

std::string FiveMDumper::GetModuleName(uintptr_t address) {
    // Alternative method to get module name without GetModuleHandleEx
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi))) {
        char moduleName[MAX_PATH];
        if (GetModuleFileNameA(reinterpret_cast<HMODULE>(mbi.AllocationBase), moduleName, MAX_PATH)) {
            std::string fullPath(moduleName);
            size_t lastSlash = fullPath.find_last_of("\\/");
            if (lastSlash != std::string::npos) {
                return fullPath.substr(lastSlash + 1);
            }
            return fullPath;
        }
    }
    return "";
}

std::string FiveMDumper::DisassembleInstruction(uintptr_t address) {
    // Simple disassembly for common x64 instructions
    if (!IsValidAddress(address)) return "Invalid Address";

    uint8_t* bytes = reinterpret_cast<uint8_t*>(address);
    std::stringstream ss;

    // Basic x64 instruction recognition
    if (bytes[0] == 0x48 && bytes[1] == 0x8B) {
        ss << "mov rax, [...]";
    } else if (bytes[0] == 0xE8) {
        int32_t offset = *reinterpret_cast<int32_t*>(bytes + 1);
        uintptr_t target = address + 5 + offset;
        ss << "call 0x" << std::hex << target;
    } else if (bytes[0] == 0xC3) {
        ss << "ret";
    } else {
        ss << "Unknown instruction: ";
        for (int i = 0; i < 8; i++) {
            ss << std::hex << std::setw(2) << std::setfill('0') << (int)bytes[i] << " ";
        }
    }

    return ss.str();
}

void FiveMDumper::AnalyzeFunction(uintptr_t address, const std::string& name) {
    if (!IsValidAddress(address)) return;

    FunctionInfo funcInfo;
    funcInfo.address = address;
    funcInfo.name = name.empty() ? ("func_" + std::to_string(address)) : name;

    // Simple function size estimation
    uintptr_t current = address;
    size_t maxSize = 0x1000; // Maximum function size to scan

    for (size_t i = 0; i < maxSize; i++) {
        if (!IsValidAddress(current + i)) break;

        uint8_t byte = *reinterpret_cast<uint8_t*>(current + i);
        if (byte == 0xC3 || byte == 0xC2) { // ret or ret imm16
            funcInfo.size = i + 1;
            break;
        }
    }

    if (funcInfo.size == 0) funcInfo.size = 0x50; // Default size

    // Generate function signature
    funcInfo.signature = DisassembleInstruction(address);

    // Find cross-references
    FindCrossReferences(address);

    m_functions[funcInfo.name] = funcInfo;
}

void FiveMDumper::AnalyzeClass(uintptr_t vtableAddress, const std::string& className) {
    if (!IsValidAddress(vtableAddress)) return;

    ClassInfo classInfo;
    classInfo.vtableAddress = vtableAddress;
    classInfo.name = className.empty() ? ("class_" + std::to_string(vtableAddress)) : className;

    // Read virtual function pointers
    uintptr_t* vtable = reinterpret_cast<uintptr_t*>(vtableAddress);
    for (int i = 0; i < 32; i++) { // Max 32 virtual functions
        if (!IsValidAddress(reinterpret_cast<uintptr_t>(&vtable[i]))) break;
        if (!IsValidAddress(vtable[i])) break;

        // Check if it points to executable memory
        MEMORY_BASIC_INFORMATION mbi;
        if (VirtualQuery(reinterpret_cast<LPCVOID>(vtable[i]), &mbi, sizeof(mbi))) {
            if (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE)) {
                classInfo.methods.push_back(vtable[i]);
            } else {
                break;
            }
        } else {
            break;
        }
    }

    // Estimate class size (basic heuristic)
    classInfo.totalSize = classInfo.methods.size() * sizeof(uintptr_t) + 0x100;

    m_classes[classInfo.name] = classInfo;
}

void FiveMDumper::FindCrossReferences(uintptr_t address) {
    // Find references to this address in executable memory
    for (const auto& region : m_memoryRegions) {
        if (region.isExecutable) {
            for (size_t i = 0; i <= region.size - sizeof(uintptr_t); i += sizeof(uintptr_t)) {
                uintptr_t* ptr = reinterpret_cast<uintptr_t*>(region.baseAddress + i);
                if (IsValidAddress(reinterpret_cast<uintptr_t>(ptr))) {
                    if (*ptr == address) {
                        // Found a reference
                        LogMessage("Cross-reference to 0x" + std::to_string(address) +
                                  " found at 0x" + std::to_string(region.baseAddress + i));
                    }
                }
            }
        }
    }
}

std::vector<uintptr_t> FiveMDumper::GetCallTargets(uintptr_t functionStart, size_t functionSize) {
    std::vector<uintptr_t> targets;

    for (size_t i = 0; i < functionSize - 5; i++) {
        uint8_t* bytes = reinterpret_cast<uint8_t*>(functionStart + i);
        if (bytes[0] == 0xE8) { // call instruction
            int32_t offset = *reinterpret_cast<int32_t*>(bytes + 1);
            uintptr_t target = functionStart + i + 5 + offset;
            if (IsValidAddress(target)) {
                targets.push_back(target);
            }
        }
    }

    return targets;
}
