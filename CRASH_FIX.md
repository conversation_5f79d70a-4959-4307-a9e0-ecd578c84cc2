# 🛡️ FiveM Dumper - Correção de Crashes

## 🚨 Problema Identificado

O FiveM crashou durante o uso do dumper com o erro:
```
Stack trace:
  0x7ff82165f916
  0x231f5e170a0
```

## 🔧 Soluções Implementadas

### 1. **Proteção de Memória Aprimorada**
- ✅ Adicionado `SafeMemoryRead()` com tratamento SEH
- ✅ Adicionado `IsValidMemoryRegion()` para validação
- ✅ Proteção contra overflow de endereços
- ✅ Verificação de ranges de memória válidos

### 2. **Pattern Scanning Seguro**
- ✅ Adicionado `__try/__except` no `FindPattern()`
- ✅ Verificação de tamanho de padrão
- ✅ Uso de `volatile` para evitar otimizações perigosas

### 3. **Configuração de Modo Seguro**
- ✅ Adicionado `ENABLE_SAFE_MODE` no `config.h`
- ✅ Limites de tamanho de região de memória
- ✅ Verificação de endereços válidos

## 🚀 Como Aplicar as Correções

### **Opção 1: Recompilar com Correções**
```bash
# O projeto já foi atualizado com as correções
build.bat
```

### **Opção 2: Usar Modo Seguro**
Edite `config.h`:
```cpp
// Habilitar modo seguro (reduz crashes)
#define ENABLE_SAFE_MODE 1

// Reduzir timeout para evitar travamentos
#define DUMPING_TIMEOUT_MS 120000  // 2 minutos

// Desabilitar análises mais agressivas
#define ENABLE_DEEP_ANALYSIS 0
#define ENABLE_VTABLE_ANALYSIS 0
```

### **Opção 3: Usar Versão Conservadora**
```cpp
// No config.h, use configurações mais conservadoras:
#define MAX_FUNCTION_SIZE 0x1000      // Reduzido de 0x10000
#define PATTERN_SCAN_TIMEOUT_MS 10000 // Reduzido de 30000
#define ENABLE_MULTITHREADED_SCANNING 0 // Desabilitar multi-thread
```

## 🎯 Uso Mais Seguro

### **1. Aguarde o FiveM Carregar Completamente**
```bash
# Aguarde pelo menos 60 segundos após entrar no servidor
# Verifique se o jogo está estável antes de injetar
```

### **2. Use Servidores de Teste**
```bash
# Prefira servidores locais ou de desenvolvimento
# Evite servidores com anti-cheat agressivo
```

### **3. Execute em Modo Administrador**
```bash
# Sempre execute como Administrador
# Clique direito → "Executar como administrador"
```

### **4. Monitore o Processo**
```bash
# Use Task Manager para monitorar uso de memória
# Se o uso de RAM do FiveM aumentar muito, pare o dumper
```

## 🔍 Diagnóstico de Crashes

### **Verificar Logs**
```bash
# Verifique FiveMDumps\log.txt para detalhes
# Procure por "Memory access violation" ou "Exception"
```

### **Identificar Padrão do Crash**
```bash
# Se crashar sempre no mesmo ponto:
# 1. Desabilite análise específica no config.h
# 2. Reduza o timeout
# 3. Use modo single-thread
```

### **Teste com Processo Simples**
```bash
# Teste primeiro com notepad.exe:
# 1. Abra notepad.exe
# 2. Modifique FiveMInjector.cpp para injetar em notepad
# 3. Verifique se a injeção funciona sem crash
```

## 📋 Checklist Anti-Crash

### **Antes de Usar:**
- [ ] FiveM carregado completamente (60+ segundos)
- [ ] Servidor estável (sem lag/desconexões)
- [ ] Executando como Administrador
- [ ] Antivírus com exceções configuradas
- [ ] Modo seguro habilitado no config.h

### **Durante o Uso:**
- [ ] Monitorar uso de memória
- [ ] Verificar logs em tempo real
- [ ] Parar se detectar instabilidade
- [ ] Aguardar conclusão completa (não interromper)

### **Após Crash:**
- [ ] Verificar FiveMDumps\log.txt
- [ ] Anotar endereços do crash
- [ ] Ajustar configurações
- [ ] Testar com configurações mais conservadoras

## 🛠️ Configurações Recomendadas para Estabilidade

### **config.h Seguro:**
```cpp
#define ENABLE_SAFE_MODE 1
#define DUMPING_TIMEOUT_MS 120000
#define ENABLE_DEEP_ANALYSIS 0
#define ENABLE_VTABLE_ANALYSIS 0
#define ENABLE_MULTITHREADED_SCANNING 0
#define PATTERN_SCAN_THREADS 1
#define MAX_FUNCTION_SIZE 0x1000
#define PATTERN_SCAN_TIMEOUT_MS 10000
#define ENABLE_EXPERIMENTAL_FEATURES 0
```

### **Uso Gradual:**
```bash
# 1. Primeiro teste: Apenas offsets básicos
# 2. Segundo teste: Adicionar análise de funções
# 3. Terceiro teste: Análise completa (se estável)
```

## 🎉 Resultado Esperado

Com essas correções, o dumper deve:
- ✅ **Não crashar** o FiveM
- ✅ **Encontrar offsets básicos** com segurança
- ✅ **Gerar logs detalhados** para diagnóstico
- ✅ **Funcionar de forma estável** em modo conservador

## 📞 Se Ainda Crashar

1. **Reduza ainda mais as configurações**
2. **Use apenas pattern scanning básico**
3. **Desabilite todas as análises avançadas**
4. **Teste em ambiente controlado (servidor local)**

---

**💡 Lembre-se:** É melhor obter alguns offsets com segurança do que crashar o jogo tentando obter todos.

**🎯 Objetivo:** Estabilidade primeiro, funcionalidades depois.
