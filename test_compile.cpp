#include <Windows.h>
#include <iostream>

// Test compilation of basic components
int main() {
    std::cout << "FiveM Dumper - Compilation Test" << std::endl;
    std::cout << "================================" << std::endl;
    
    // Test basic Windows API
    HMODULE hModule = GetModuleHandle(nullptr);
    if (hModule) {
        std::cout << "[OK] GetModuleHandle works" << std::endl;
    }
    
    // Test memory operations
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(reinterpret_cast<LPCVOID>(hModule), &mbi, sizeof(mbi))) {
        std::cout << "[OK] VirtualQuery works" << std::endl;
    }
    
    // Test process enumeration
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        std::cout << "[OK] CreateToolhelp32Snapshot works" << std::endl;
        CloseHandle(hSnapshot);
    }
    
    std::cout << "================================" << std::endl;
    std::cout << "All basic components working!" << std::endl;
    std::cout << "Ready to compile FiveM Dumper!" << std::endl;
    
    return 0;
}
