// FiveM Advanced Dumper - Professional Memory Analysis Tool
// Author: Advanced Security Research
// Version: 2.0

#include "pch.h"
#include "FiveMDumper.h"
#include <thread>
#include <chrono>

// Global dumper instance
std::unique_ptr<FiveMDumper> g_dumper = nullptr;

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);

        // Initialize dumper in separate thread to avoid blocking
        std::thread([hModule]() {
            try {
                g_dumper = std::make_unique<FiveMDumper>(hModule);
                g_dumper->Initialize();
                g_dumper->StartDumping();
            }
            catch (const std::exception& e) {
                MessageBoxA(nullptr, e.what(), "FiveM Dumper Error", MB_ICONERROR);
            }
        }).detach();
        break;

    case DLL_PROCESS_DETACH:
        if (g_dumper) {
            g_dumper->Cleanup();
            g_dumper.reset();
        }
        break;
    }
    return TRUE;
}

