@echo off
echo ========================================
echo    FiveM Advanced Dumper Build Script
echo ========================================
echo.

:: Check if Visual Studio is installed
where msbuild >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] MSBuild not found. Please install Visual Studio Build Tools.
    echo.
    echo Download from: https://visualstudio.microsoft.com/downloads/
    pause
    exit /b 1
)

:: Set build configuration
set CONFIG=Release
set PLATFORM=x64

echo [INFO] Building FiveM Dumper...
echo [INFO] Configuration: %CONFIG%
echo [INFO] Platform: %PLATFORM%
echo.

:: Build the main DLL project
echo [STEP 1/3] Building OffsetsDumper.dll...
cd OffsetsDumper
msbuild OffsetsDumper.sln /p:Configuration=%CONFIG% /p:Platform=%PLATFORM% /verbosity:minimal

if %errorlevel% neq 0 (
    echo [ERROR] Failed to build OffsetsDumper.dll
    cd ..
    pause
    exit /b 1
)

cd ..
echo [SUCCESS] OffsetsDumper.dll built successfully!
echo.

:: Build the injector
echo [STEP 2/3] Building FiveMInjector.exe...

:: Check if g++ is available
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] g++ not found. Trying with cl.exe (MSVC)...
    
    :: Try to find MSVC compiler
    where cl >nul 2>nul
    if %errorlevel% neq 0 (
        echo [ERROR] No C++ compiler found. Please install:
        echo   - MinGW-w64 (for g++)
        echo   - Visual Studio Build Tools (for cl.exe)
        pause
        exit /b 1
    )
    
    :: Compile with MSVC
    cl /EHsc /O2 FiveMInjector.cpp /Fe:FiveMInjector.exe /link psapi.lib
) else (
    :: Compile with g++
    g++ -std=c++17 -O2 -o FiveMInjector.exe FiveMInjector.cpp -lpsapi -static-libgcc -static-libstdc++
)

if %errorlevel% neq 0 (
    echo [ERROR] Failed to build FiveMInjector.exe
    pause
    exit /b 1
)

echo [SUCCESS] FiveMInjector.exe built successfully!
echo.

:: Create output directory structure
echo [STEP 3/3] Setting up output directories...
if not exist "FiveMDumps" mkdir FiveMDumps
if not exist "Release" mkdir Release

:: Copy files to release directory
copy "OffsetsDumper\x64\Release\OffsetsDumper.dll" "Release\" >nul 2>nul
copy "FiveMInjector.exe" "Release\" >nul 2>nul
copy "README.md" "Release\" >nul 2>nul

echo [SUCCESS] Files copied to Release directory!
echo.

:: Display build summary
echo ========================================
echo           BUILD COMPLETED
echo ========================================
echo.
echo Built files:
echo   - OffsetsDumper.dll (Main dumper library)
echo   - FiveMInjector.exe (Injection tool)
echo.
echo Output locations:
echo   - OffsetsDumper\x64\Release\OffsetsDumper.dll
echo   - FiveMInjector.exe
echo   - Release\ (All files copied here)
echo.
echo Usage:
echo   1. Start FiveM and join a server
echo   2. Run FiveMInjector.exe as Administrator
echo   3. Check FiveMDumps\ folder for results
echo.
echo ========================================

:: Clean up temporary files
if exist "*.obj" del "*.obj" >nul 2>nul
if exist "*.pdb" del "*.pdb" >nul 2>nul

echo [INFO] Build completed successfully!
echo Press any key to exit...
pause >nul
