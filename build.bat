@echo off
echo ========================================
echo    FiveM Advanced Dumper Build Script
echo ========================================
echo.

:: Check if Visual Studio is installed
where msbuild >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] MSBuild not found. Please install Visual Studio Build Tools.
    echo.
    echo Download from: https://visualstudio.microsoft.com/downloads/
    pause
    exit /b 1
)

:: Set build configuration
set CONFIG=Release
set PLATFORM=x64

echo [INFO] Building FiveM Dumper...
echo [INFO] Configuration: %CONFIG%
echo [INFO] Platform: %PLATFORM%
echo.

:: Build the main DLL project
echo [STEP 1/3] Building OffsetsDumper.dll...
cd OffsetsDumper
msbuild OffsetsDumper.sln /p:Configuration=%CONFIG% /p:Platform=%PLATFORM% /verbosity:minimal

if %errorlevel% neq 0 (
    echo [ERROR] Failed to build OffsetsDumper.dll
    cd ..
    pause
    exit /b 1
)

cd ..
echo [SUCCESS] OffsetsDumper.dll built successfully!
echo.

:: Build the injectors
echo [STEP 2/3] Building injectors...

:: Build regular injector
echo [INFO] Building FiveMInjector.exe...
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    where cl >nul 2>nul
    if %errorlevel% neq 0 (
        echo [ERROR] No C++ compiler found. Please install Visual Studio Build Tools or MinGW-w64
        pause
        exit /b 1
    )
    cl /EHsc /O2 FiveMInjector.cpp /Fe:FiveMInjector.exe /link psapi.lib >nul 2>nul
) else (
    g++ -std=c++17 -O2 -o FiveMInjector.exe FiveMInjector.cpp -lpsapi -static-libgcc -static-libstdc++ >nul 2>nul
)

:: Build safe injector
echo [INFO] Building FiveMInjector_Safe.exe...
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    cl /EHsc /O2 FiveMInjector_Safe.cpp /Fe:FiveMInjector_Safe.exe /link psapi.lib >nul 2>nul
) else (
    g++ -std=c++17 -O2 -o FiveMInjector_Safe.exe FiveMInjector_Safe.cpp -lpsapi -static-libgcc -static-libstdc++ >nul 2>nul
)

if exist "FiveMInjector.exe" (
    echo [SUCCESS] FiveMInjector.exe built successfully!
) else (
    echo [ERROR] Failed to build FiveMInjector.exe
)

if exist "FiveMInjector_Safe.exe" (
    echo [SUCCESS] FiveMInjector_Safe.exe built successfully!
) else (
    echo [WARNING] Failed to build FiveMInjector_Safe.exe
)
echo.

:: Create output directory structure
echo [STEP 3/3] Setting up output directories...
if not exist "FiveMDumps" mkdir FiveMDumps
if not exist "Release" mkdir Release

:: Copy files to release directory
copy "OffsetsDumper\x64\Release\OffsetsDumper.dll" "Release\" >nul 2>nul
if exist "FiveMInjector.exe" copy "FiveMInjector.exe" "Release\" >nul 2>nul
if exist "FiveMInjector_Safe.exe" copy "FiveMInjector_Safe.exe" "Release\" >nul 2>nul
copy "README.md" "Release\" >nul 2>nul
copy "CRASH_FIX.md" "Release\" >nul 2>nul
copy "config.h" "Release\" >nul 2>nul

echo [SUCCESS] Files copied to Release directory!
echo.

:: Display build summary
echo ========================================
echo           BUILD COMPLETED
echo ========================================
echo.
echo Built files:
echo   - OffsetsDumper.dll (Main dumper library)
if exist "FiveMInjector.exe" echo   - FiveMInjector.exe (Full version)
if exist "FiveMInjector_Safe.exe" echo   - FiveMInjector_Safe.exe (Recommended)
echo.
echo Output locations:
echo   - OffsetsDumper\x64\Release\OffsetsDumper.dll
if exist "FiveMInjector.exe" echo   - FiveMInjector.exe
if exist "FiveMInjector_Safe.exe" echo   - FiveMInjector_Safe.exe
echo   - Release\ (All files copied here)
echo.
echo RECOMMENDED USAGE (to avoid crashes):
echo   1. Start FiveM and join a server
echo   2. Wait 60+ seconds for game to stabilize
echo   3. Run FiveMInjector_Safe.exe as Administrator
echo   4. Wait for dumping to complete (2-5 minutes)
echo   5. Check FiveMDumps\ folder for results
echo.
echo If crashes occur, see CRASH_FIX.md for solutions
echo.
echo ========================================

:: Clean up temporary files
if exist "*.obj" del "*.obj" >nul 2>nul
if exist "*.pdb" del "*.pdb" >nul 2>nul

echo [INFO] Build completed successfully!
echo Press any key to exit...
pause >nul
