# 🚀 Quick Start Guide - FiveM Advanced Dumper

## ⚡ Compilação Rápida

### Método 1: Script Automático (Recomendado)
```bash
# Execute o script de build
build.bat
```

### Método 2: Manual
```bash
# 1. Compile a DLL
cd OffsetsDumper
msbuild OffsetsDumper.sln /p:Configuration=Release /p:Platform=x64

# 2. Compile o injetor
cd ..
g++ -o FiveMInjector.exe FiveMInjector.cpp -lpsapi -static
```

## 🎯 Uso Rápido

### Passo 1: Preparação
1. **Inicie o FiveM** e entre em qualquer servidor
2. **Execute como Administrador** o terminal/prompt

### Passo 2: Execução
```bash
# Execute o injetor
FiveMInjector.exe
```

### Passo 3: Resultados
Verifique a pasta `FiveMDumps/` para os resultados:
- `offsets.txt` - Offsets principais
- `FiveMOffsets.h` - Header C++ pronto para uso
- `FiveM.CT` - Tabela do Cheat Engine

## 📁 Estrutura de Arquivos

```
fivem dumper/
├── OffsetsDumper/           # Projeto principal (DLL)
│   └── OffsetsDumper/
│       ├── FiveMDumper.h    # Header principal
│       ├── FiveMDumper.cpp  # Implementação
│       ├── dllmain.cpp      # Entry point
│       └── pch.h            # Headers pré-compilados
├── FiveMInjector.cpp        # Injetor standalone
├── build.bat               # Script de compilação
├── config.h                # Configurações
├── ExampleUsage.cpp        # Exemplo de uso
└── README.md               # Documentação completa
```

## ⚙️ Configuração Rápida

Edite `config.h` para personalizar:

```cpp
// Habilitar debug
#define ENABLE_DEBUG_OUTPUT 1

// Habilitar anti-detecção
#define ENABLE_ANTI_DETECTION 1

// Timeout de análise (5 minutos)
#define DUMPING_TIMEOUT_MS 300000
```

## 🔧 Troubleshooting Rápido

### ❌ "Failed to open process"
```bash
# Solução: Execute como Administrador
# Clique direito → "Executar como administrador"
```

### ❌ "DLL injection failed"
```bash
# Verifique se compilou corretamente
dir OffsetsDumper\x64\Release\OffsetsDumper.dll

# Se não existir, recompile:
build.bat
```

### ❌ "No patterns found"
```bash
# FiveM pode ter atualizado
# Aguarde 30 segundos após entrar no servidor
# Tente novamente
```

### ❌ "MSBuild not found"
```bash
# Instale Visual Studio Build Tools
# Download: https://visualstudio.microsoft.com/downloads/
```

## 📊 Exemplo de Output

### offsets.txt
```
WorldPtr = 0x2485A28
EntityPool = 0x1F8B3A8
PedPool = 0x1F8B3B0
VehiclePool = 0x1F8B3B8
LocalPlayer = 0x1F8B3E0
```

### FiveMOffsets.h
```cpp
namespace Offsets {
    constexpr uintptr_t WorldPtr = 0x2485A28;
    constexpr uintptr_t EntityPool = 0x1F8B3A8;
    constexpr uintptr_t PedPool = 0x1F8B3B0;
}
```

## 🎮 Uso dos Offsets

```cpp
#include "FiveMDumps/FiveMOffsets.h"

// Obter base do processo
uintptr_t base = (uintptr_t)GetModuleHandle(nullptr);

// Ler World Pointer
uintptr_t worldPtr = *(uintptr_t*)(base + Offsets::WorldPtr);

// Ler Entity Pool
uintptr_t entityPool = *(uintptr_t*)(base + Offsets::EntityPool);
```

## 🛡️ Recursos de Segurança

- ✅ **Anti-Detecção**: PEB hiding, anti-debug bypass
- ✅ **Stealth Mode**: Execução furtiva sem rastros
- ✅ **Memory Protection**: Proteção contra análise
- ✅ **Self-Destruct**: Auto-remoção após análise (opcional)

## 📈 Performance

- ⚡ **Multi-threading**: Análise paralela
- 🧠 **Memory Caching**: Cache inteligente
- 🔍 **Pattern Optimization**: Busca otimizada
- 📊 **Progress Tracking**: Acompanhamento em tempo real

## 🔗 Links Úteis

- [Documentação Completa](README.md)
- [Exemplo de Uso](ExampleUsage.cpp)
- [Configurações](config.h)

## 💡 Dicas Profissionais

1. **Sempre execute como Administrador** para máxima compatibilidade
2. **Aguarde o FiveM carregar completamente** antes de injetar
3. **Desative antivírus temporariamente** se houver falsos positivos
4. **Use em ambiente controlado** para pesquisa e educação
5. **Mantenha backups** dos offsets encontrados

## 🚨 Avisos Importantes

- ⚠️ **Uso Educacional**: Apenas para pesquisa e aprendizado
- ⚠️ **Anti-Cheat**: Pode ser detectado por sistemas avançados
- ⚠️ **Atualizações**: Offsets mudam com updates do FiveM
- ⚠️ **Responsabilidade**: Use com ética e responsabilidade

---

**🎯 Objetivo**: Fornecer uma ferramenta profissional para análise de memória e pesquisa em segurança do FiveM.

**👨‍💻 Desenvolvido por**: Advanced Security Research Team

**📅 Versão**: 2.0 - Professional Edition
