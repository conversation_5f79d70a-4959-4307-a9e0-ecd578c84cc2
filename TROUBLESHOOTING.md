# 🔧 Troubleshooting - FiveM Advanced Dumper

## 🚨 Problemas de Compilação

### ❌ Erro: "MSBuild not found"

**Solução:**
```bash
# 1. Instale Visual Studio Build Tools
# Download: https://visualstudio.microsoft.com/downloads/

# 2. Ou use o teste de detecção automática
test_build.bat

# 3. Ou compile manualmente com g++
g++ -std=c++17 -O2 -o FiveMInjector.exe FiveMInjector.cpp -lpsapi -static
```

### ❌ Erro: "C2011 redefinição do tipo"

**Causa:** Conflito com headers do Windows

**Solução:** Já corrigido no código atual. Se persistir:
```cpp
// Adicione no topo do arquivo problemático
#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
```

### ❌ Erro: "cannot open source file"

**Solução:**
```bash
# 1. Verifique se todos os arquivos estão presentes
dir OffsetsDumper\OffsetsDumper\*.h
dir OffsetsDumper\OffsetsDumper\*.cpp

# 2. Recrie o projeto se necessário
# Abra Visual Studio → File → New → Project from Existing Code
```

### ❌ Erro: "LNK2019 unresolved external symbol"

**Solução:**
```cpp
// Adicione as bibliotecas necessárias
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "kernel32.lib")
#pragma comment(lib, "user32.lib")
```

## 🚨 Problemas de Execução

### ❌ "Failed to open process"

**Causas e Soluções:**

1. **Permissões insuficientes**
   ```bash
   # Execute como Administrador
   # Clique direito → "Executar como administrador"
   ```

2. **FiveM não está rodando**
   ```bash
   # 1. Inicie o FiveM
   # 2. Entre em qualquer servidor
   # 3. Aguarde carregar completamente
   # 4. Execute o injetor
   ```

3. **Antivírus bloqueando**
   ```bash
   # Adicione exceção no antivírus para:
   # - FiveMInjector.exe
   # - OffsetsDumper.dll
   # - Pasta do projeto
   ```

### ❌ "DLL injection failed"

**Soluções:**

1. **Verifique se a DLL existe**
   ```bash
   dir OffsetsDumper\x64\Release\OffsetsDumper.dll
   ```

2. **Recompile a DLL**
   ```bash
   cd OffsetsDumper
   msbuild OffsetsDumper.sln /p:Configuration=Release /p:Platform=x64
   ```

3. **Use injetor alternativo**
   ```bash
   # Process Hacker, Cheat Engine, ou outro injetor
   ```

### ❌ "No patterns found"

**Causas e Soluções:**

1. **FiveM atualizado**
   ```bash
   # Os padrões podem ter mudado
   # Aguarde atualização do dumper ou
   # Modifique os padrões em FiveMDumper.cpp
   ```

2. **Processo não carregado completamente**
   ```bash
   # Aguarde 30-60 segundos após entrar no servidor
   # Tente novamente
   ```

3. **Versão incompatível**
   ```bash
   # Verifique se está usando FiveM atualizado
   # Teste em servidor diferente
   ```

## 🚨 Problemas de Detecção

### ❌ "Process not found"

**Soluções:**
```bash
# 1. Verifique processos FiveM rodando
tasklist | findstr -i fivem

# 2. Processos suportados:
# - FiveM.exe
# - FiveM_GTAProcess.exe
# - FiveM_ChromiumProcess.exe
# - CitizenFX.exe
# - GTA5.exe
```

### ❌ Anti-Cheat detectou

**Prevenção:**
```cpp
// Já implementado no código:
// - PEB Hiding
// - Anti-Debug Bypass
// - Stealth Mode
// - Memory Protection
```

**Soluções adicionais:**
1. Use em servidores de teste
2. Desative temporariamente anti-cheat (se possível)
3. Use em modo offline/singleplayer

## 🚨 Problemas de Output

### ❌ "FiveMDumps folder empty"

**Soluções:**
```bash
# 1. Verifique permissões de escrita
# 2. Execute como Administrador
# 3. Verifique se o dumper completou a execução
# 4. Verifique logs em FiveMDumps\log.txt
```

### ❌ "Offsets incorretos"

**Verificação:**
```cpp
// Teste os offsets encontrados:
uintptr_t base = (uintptr_t)GetModuleHandle(nullptr);
uintptr_t worldPtr = *(uintptr_t*)(base + offset);

// Se worldPtr for 0 ou inválido, o offset está errado
```

## 🛠️ Ferramentas de Debug

### 1. **Teste de Compilação**
```bash
test_build.bat
```

### 2. **Debug Mode**
```cpp
// Modifique config.h:
#define ENABLE_DEBUG_OUTPUT 1
#define ENABLE_CONSOLE_WINDOW 1
```

### 3. **Log Detalhado**
```cpp
// Verifique FiveMDumps\log.txt para detalhes
```

### 4. **Verificação Manual**
```bash
# Use Process Hacker ou Process Explorer para:
# - Verificar se a DLL foi injetada
# - Verificar permissões do processo
# - Verificar módulos carregados
```

## 🔍 Diagnóstico Avançado

### **Verificar Integridade dos Arquivos**
```bash
# Verifique se todos os arquivos estão presentes:
dir /s *.h *.cpp *.sln *.vcxproj
```

### **Verificar Dependências**
```bash
# Use Dependency Walker ou similar para verificar:
# - OffsetsDumper.dll
# - FiveMInjector.exe
```

### **Teste com Processo Simples**
```cpp
// Teste a injeção em notepad.exe primeiro:
// 1. Abra notepad.exe
// 2. Modifique FiveMInjector.cpp para injetar em notepad
// 3. Teste se a injeção funciona
```

## 📞 Suporte Adicional

### **Informações para Relatório de Bug**
```
1. Versão do Windows: ___________
2. Versão do Visual Studio: ___________
3. Versão do FiveM: ___________
4. Erro exato: ___________
5. Log completo: (anexar FiveMDumps\log.txt)
6. Passos para reproduzir: ___________
```

### **Logs Úteis**
```bash
# Colete estes logs para diagnóstico:
# - FiveMDumps\log.txt
# - Event Viewer → Windows Logs → Application
# - Output do Visual Studio (se compilando)
```

### **Teste de Ambiente**
```bash
# Execute para verificar ambiente:
test_build.bat

# Deve mostrar:
# [✓] Basic C++ compilation: PASSED
# [✓] Executable runtime: PASSED
# [✓] MSBuild availability: FOUND
```

## 🔄 Reset Completo

Se nada funcionar, faça um reset completo:

```bash
# 1. Delete todos os arquivos temporários
del /s *.obj *.pdb *.exe *.dll *.log

# 2. Limpe o projeto
cd OffsetsDumper
msbuild OffsetsDumper.sln /t:Clean

# 3. Recompile tudo
msbuild OffsetsDumper.sln /p:Configuration=Release /p:Platform=x64

# 4. Teste novamente
cd ..
FiveMInjector.exe
```

---

**💡 Dica:** Sempre execute como Administrador e aguarde o FiveM carregar completamente antes de usar o dumper.

**⚠️ Lembrete:** Use apenas para fins educacionais e de pesquisa em ambientes controlados.
