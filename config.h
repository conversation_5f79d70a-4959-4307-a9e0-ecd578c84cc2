#pragma once

/*
    FiveM Advanced Dumper - Configuration File
    
    Configure various aspects of the dumper behavior here.
    Modify these settings according to your needs.
*/

// ============================================================================
// GENERAL CONFIGURATION
// ============================================================================

// Enable/disable debug output
#define ENABLE_DEBUG_OUTPUT 1

// Enable/disable console window for debug output
#define ENABLE_CONSOLE_WINDOW 0

// Maximum time to wait for dumping completion (milliseconds)
#define DUMPING_TIMEOUT_MS 300000  // 5 minutes

// Enable/disable anti-detection features
#define ENABLE_ANTI_DETECTION 1

// ============================================================================
// PATTERN SCANNING CONFIGURATION
// ============================================================================

// Maximum number of patterns to search simultaneously
#define MAX_CONCURRENT_PATTERNS 50

// Pattern scanning timeout per region (milliseconds)
#define PATTERN_SCAN_TIMEOUT_MS 30000

// Enable/disable multi-threaded pattern scanning
#define ENABLE_MULTITHREADED_SCANNING 1

// Number of threads for pattern scanning (0 = auto-detect)
#define PATTERN_SCAN_THREADS 0

// ============================================================================
// MEMORY ANALYSIS CONFIGURATION
// ============================================================================

// Maximum function size to analyze (bytes)
#define MAX_FUNCTION_SIZE 0x10000

// Maximum number of virtual functions per class
#define MAX_VIRTUAL_FUNCTIONS 128

// Enable/disable deep memory analysis
#define ENABLE_DEEP_ANALYSIS 1

// Minimum memory region size to analyze
#define MIN_REGION_SIZE 0x1000

// ============================================================================
// OUTPUT CONFIGURATION
// ============================================================================

// Output directory name
#define OUTPUT_DIRECTORY "FiveMDumps"

// Enable/disable specific output files
#define GENERATE_OFFSETS_FILE 1
#define GENERATE_FUNCTIONS_FILE 1
#define GENERATE_CLASSES_FILE 1
#define GENERATE_HEADER_FILE 1
#define GENERATE_CHEAT_ENGINE_TABLE 1

// Output file names
#define OFFSETS_FILENAME "offsets.txt"
#define FUNCTIONS_FILENAME "functions.txt"
#define CLASSES_FILENAME "classes.txt"
#define HEADER_FILENAME "FiveMOffsets.h"
#define CHEAT_ENGINE_FILENAME "FiveM.CT"
#define LOG_FILENAME "log.txt"

// ============================================================================
// FIVEM SPECIFIC CONFIGURATION
// ============================================================================

// Known FiveM process names to search for
#define FIVEM_PROCESS_NAMES { \
    "FiveM.exe", \
    "FiveM_GTAProcess.exe", \
    "FiveM_ChromiumProcess.exe", \
    "CitizenFX.exe", \
    "GTA5.exe" \
}

// Enable/disable specific FiveM analysis modules
#define ANALYZE_ENTITY_SYSTEM 1
#define ANALYZE_SCRIPT_ENGINE 1
#define ANALYZE_NETWORK_STRUCTURES 1
#define ANALYZE_RENDERING_SYSTEM 1
#define ANALYZE_WEAPON_SYSTEM 1
#define ANALYZE_VEHICLE_SYSTEM 1
#define ANALYZE_PED_SYSTEM 1

// ============================================================================
// ADVANCED FEATURES CONFIGURATION
// ============================================================================

// Enable/disable advanced analysis techniques
#define ENABLE_VTABLE_ANALYSIS 1
#define ENABLE_CROSS_REFERENCE_ANALYSIS 1
#define ENABLE_STATIC_ANALYSIS 1
#define ENABLE_IMPORT_TABLE_ANALYSIS 1
#define ENABLE_HIDDEN_FUNCTION_DETECTION 1
#define ENABLE_CLASS_RECONSTRUCTION 1

// Enable/disable experimental features
#define ENABLE_EXPERIMENTAL_FEATURES 0
#define ENABLE_KERNEL_MODE_ANALYSIS 0
#define ENABLE_HARDWARE_BREAKPOINTS 0

// ============================================================================
// SECURITY CONFIGURATION
// ============================================================================

// Enable/disable security features
#define ENABLE_PEB_HIDING 1
#define ENABLE_ANTI_DEBUG_BYPASS 1
#define ENABLE_MEMORY_PROTECTION 1
#define ENABLE_STEALTH_MODE 1

// Self-destruct after analysis (for security)
#define ENABLE_SELF_DESTRUCT 0
#define SELF_DESTRUCT_DELAY_MS 60000  // 1 minute

// ============================================================================
// PERFORMANCE CONFIGURATION
// ============================================================================

// Memory scanning chunk size
#define MEMORY_SCAN_CHUNK_SIZE 0x100000  // 1MB

// Maximum memory usage for caching (bytes)
#define MAX_CACHE_SIZE 0x10000000  // 256MB

// Enable/disable performance optimizations
#define ENABLE_MEMORY_CACHING 1
#define ENABLE_LAZY_LOADING 1
#define ENABLE_PARALLEL_PROCESSING 1

// ============================================================================
// ERROR HANDLING CONFIGURATION
// ============================================================================

// Maximum number of retries for failed operations
#define MAX_OPERATION_RETRIES 3

// Delay between retries (milliseconds)
#define RETRY_DELAY_MS 1000

// Enable/disable graceful error handling
#define ENABLE_GRACEFUL_ERROR_HANDLING 1

// Continue analysis on non-critical errors
#define CONTINUE_ON_ERRORS 1

// ============================================================================
// LOGGING CONFIGURATION
// ============================================================================

// Log levels
#define LOG_LEVEL_ERROR   1
#define LOG_LEVEL_WARNING 2
#define LOG_LEVEL_INFO    3
#define LOG_LEVEL_DEBUG   4
#define LOG_LEVEL_VERBOSE 5

// Current log level
#define CURRENT_LOG_LEVEL LOG_LEVEL_INFO

// Enable/disable log file rotation
#define ENABLE_LOG_ROTATION 1
#define MAX_LOG_FILE_SIZE 0x1000000  // 16MB

// ============================================================================
// COMPATIBILITY CONFIGURATION
// ============================================================================

// Target Windows version
#define TARGET_WINDOWS_VERSION 0x0A00  // Windows 10

// Enable/disable compatibility modes
#define ENABLE_WINDOWS_7_COMPATIBILITY 0
#define ENABLE_WINDOWS_8_COMPATIBILITY 0
#define ENABLE_WINDOWS_10_COMPATIBILITY 1
#define ENABLE_WINDOWS_11_COMPATIBILITY 1

// ============================================================================
// VALIDATION MACROS
// ============================================================================

// Validate configuration
#if PATTERN_SCAN_THREADS < 0
#error "PATTERN_SCAN_THREADS must be >= 0"
#endif

#if MAX_FUNCTION_SIZE < 0x100
#error "MAX_FUNCTION_SIZE must be >= 256 bytes"
#endif

#if DUMPING_TIMEOUT_MS < 10000
#error "DUMPING_TIMEOUT_MS must be >= 10 seconds"
#endif

// ============================================================================
// UTILITY MACROS
// ============================================================================

// Debug output macro
#if ENABLE_DEBUG_OUTPUT
#define DEBUG_PRINT(msg) OutputDebugStringA(("[DEBUG] " + std::string(msg) + "\n").c_str())
#else
#define DEBUG_PRINT(msg) ((void)0)
#endif

// Error handling macro
#if ENABLE_GRACEFUL_ERROR_HANDLING
#define HANDLE_ERROR(msg) LogMessage("ERROR: " + std::string(msg))
#else
#define HANDLE_ERROR(msg) throw std::runtime_error(msg)
#endif

// Performance timing macro
#define TIME_OPERATION(name, operation) \
    do { \
        auto start = std::chrono::high_resolution_clock::now(); \
        operation; \
        auto end = std::chrono::high_resolution_clock::now(); \
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start); \
        LogMessage(name + " took " + std::to_string(duration.count()) + "ms"); \
    } while(0)

// ============================================================================
// VERSION INFORMATION
// ============================================================================

#define DUMPER_VERSION_MAJOR 2
#define DUMPER_VERSION_MINOR 0
#define DUMPER_VERSION_PATCH 0
#define DUMPER_VERSION_BUILD 1

#define DUMPER_VERSION_STRING "2.0.0.1"
#define DUMPER_NAME "FiveM Advanced Dumper"
#define DUMPER_AUTHOR "Advanced Security Research Team"
#define DUMPER_COPYRIGHT "Copyright (C) 2024"

// ============================================================================
// END OF CONFIGURATION
// ============================================================================
