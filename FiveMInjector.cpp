#include <Windows.h>
#include <TlHelp32.h>
#include <iostream>
#include <string>
#include <vector>

class FiveMInjector {
private:
    DWORD m_processId;
    HANDLE m_processHandle;
    std::string m_dllPath;

public:
    FiveMInjector() : m_processId(0), m_processHandle(nullptr) {}
    
    ~FiveMInjector() {
        if (m_processHandle) {
            CloseHandle(m_processHandle);
        }
    }

    bool FindFiveMProcess() {
        std::vector<std::string> fivemProcessNames = {
            "FiveM.exe",
            "FiveM_GTAProcess.exe", 
            "FiveM_ChromiumProcess.exe",
            "CitizenFX.exe",
            "GTA5.exe"
        };

        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            std::cout << "[-] Failed to create process snapshot" << std::endl;
            return false;
        }

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName(pe32.szExeFile);
                
                for (const auto& fivemName : fivemProcessNames) {
                    if (processName.find(fivemName) != std::string::npos) {
                        m_processId = pe32.th32ProcessID;
                        std::cout << "[+] Found FiveM process: " << processName 
                                 << " (PID: " << m_processId << ")" << std::endl;
                        CloseHandle(hSnapshot);
                        return true;
                    }
                }
            } while (Process32Next(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
        std::cout << "[-] FiveM process not found" << std::endl;
        return false;
    }

    bool OpenTargetProcess() {
        if (m_processId == 0) {
            std::cout << "[-] No target process ID" << std::endl;
            return false;
        }

        m_processHandle = OpenProcess(PROCESS_ALL_ACCESS, FALSE, m_processId);
        if (!m_processHandle) {
            std::cout << "[-] Failed to open process. Error: " << GetLastError() << std::endl;
            std::cout << "[-] Try running as Administrator" << std::endl;
            return false;
        }

        std::cout << "[+] Successfully opened target process" << std::endl;
        return true;
    }

    bool InjectDLL(const std::string& dllPath) {
        if (!m_processHandle) {
            std::cout << "[-] Process handle is invalid" << std::endl;
            return false;
        }

        // Get full path to DLL
        char fullPath[MAX_PATH];
        if (!GetFullPathNameA(dllPath.c_str(), MAX_PATH, fullPath, nullptr)) {
            std::cout << "[-] Failed to get full DLL path" << std::endl;
            return false;
        }

        // Check if DLL exists
        if (GetFileAttributesA(fullPath) == INVALID_FILE_ATTRIBUTES) {
            std::cout << "[-] DLL file not found: " << fullPath << std::endl;
            return false;
        }

        std::cout << "[+] Injecting DLL: " << fullPath << std::endl;

        // Allocate memory in target process
        size_t pathLength = strlen(fullPath) + 1;
        LPVOID remotePath = VirtualAllocEx(m_processHandle, nullptr, pathLength, 
                                          MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        
        if (!remotePath) {
            std::cout << "[-] Failed to allocate memory in target process" << std::endl;
            return false;
        }

        // Write DLL path to target process
        if (!WriteProcessMemory(m_processHandle, remotePath, fullPath, pathLength, nullptr)) {
            std::cout << "[-] Failed to write DLL path to target process" << std::endl;
            VirtualFreeEx(m_processHandle, remotePath, 0, MEM_RELEASE);
            return false;
        }

        // Get LoadLibraryA address
        HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
        if (!hKernel32) {
            std::cout << "[-] Failed to get kernel32.dll handle" << std::endl;
            VirtualFreeEx(m_processHandle, remotePath, 0, MEM_RELEASE);
            return false;
        }

        LPVOID loadLibraryAddr = GetProcAddress(hKernel32, "LoadLibraryA");
        if (!loadLibraryAddr) {
            std::cout << "[-] Failed to get LoadLibraryA address" << std::endl;
            VirtualFreeEx(m_processHandle, remotePath, 0, MEM_RELEASE);
            return false;
        }

        // Create remote thread
        HANDLE hThread = CreateRemoteThread(m_processHandle, nullptr, 0,
                                           reinterpret_cast<LPTHREAD_START_ROUTINE>(loadLibraryAddr),
                                           remotePath, 0, nullptr);
        
        if (!hThread) {
            std::cout << "[-] Failed to create remote thread. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(m_processHandle, remotePath, 0, MEM_RELEASE);
            return false;
        }

        std::cout << "[+] Remote thread created successfully" << std::endl;
        std::cout << "[+] Waiting for injection to complete..." << std::endl;

        // Wait for thread completion
        DWORD waitResult = WaitForSingleObject(hThread, 10000); // 10 second timeout
        
        if (waitResult == WAIT_TIMEOUT) {
            std::cout << "[-] Injection timed out" << std::endl;
            TerminateThread(hThread, 0);
            CloseHandle(hThread);
            VirtualFreeEx(m_processHandle, remotePath, 0, MEM_RELEASE);
            return false;
        }

        // Get thread exit code
        DWORD exitCode;
        if (GetExitCodeThread(hThread, &exitCode)) {
            if (exitCode != 0) {
                std::cout << "[+] DLL injection successful!" << std::endl;
                std::cout << "[+] Module handle: 0x" << std::hex << exitCode << std::endl;
            } else {
                std::cout << "[-] DLL injection failed (LoadLibrary returned NULL)" << std::endl;
                CloseHandle(hThread);
                VirtualFreeEx(m_processHandle, remotePath, 0, MEM_RELEASE);
                return false;
            }
        }

        // Cleanup
        CloseHandle(hThread);
        VirtualFreeEx(m_processHandle, remotePath, 0, MEM_RELEASE);
        
        return true;
    }

    void ShowBanner() {
        std::cout << R"(
  ______ _           __  __   _____                                
 |  ____(_)         |  \/  | |  __ \                               
 | |__   ___   _____|  \  / | | |  | |_   _ _ __ ___  _ __   ___ _ __ 
 |  __| | \ \ / / _ \  |\/| | | |  | | | | | '_ ` _ \| '_ \ / _ \ '__|
 | |    | |\ V /  __/ |  | | | |__| | |_| | | | | | | |_) |  __/ |   
 |_|    |_| \_/ \___|_|  |_| |_____/ \__,_|_| |_| |_| .__/ \___|_|   
                                                    | |              
                                                    |_|              
        Advanced FiveM Memory Dumper v2.0
        Professional Security Research Tool
        )" << std::endl;
    }
};

int main() {
    FiveMInjector injector;
    injector.ShowBanner();

    std::cout << "[*] Starting FiveM Dumper Injector..." << std::endl;

    // Find FiveM process
    if (!injector.FindFiveMProcess()) {
        std::cout << "[-] Please start FiveM and try again" << std::endl;
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }

    // Open target process
    if (!injector.OpenTargetProcess()) {
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }

    // Inject DLL
    std::string dllPath = "OffsetsDumper\\x64\\Release\\OffsetsDumper.dll";
    if (!injector.InjectDLL(dllPath)) {
        std::cout << "[-] Injection failed" << std::endl;
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }

    std::cout << "[+] FiveM Dumper injected successfully!" << std::endl;
    std::cout << "[+] Check the 'FiveMDumps' folder for results" << std::endl;
    std::cout << "[+] The dumper will run automatically and generate reports" << std::endl;
    std::cout << "Press any key to exit..." << std::endl;
    std::cin.get();

    return 0;
}
