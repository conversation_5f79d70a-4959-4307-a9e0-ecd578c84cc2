@echo off
echo ========================================
echo    FiveM Dumper - Compilation Test
echo ========================================
echo.

:: Test basic compilation
echo [TEST 1] Testing basic C++ compilation...
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo [INFO] MSVC not found, trying g++...
    where g++ >nul 2>nul
    if %errorlevel% neq 0 (
        echo [ERROR] No C++ compiler found!
        echo Please install Visual Studio Build Tools or MinGW-w64
        pause
        exit /b 1
    )
    
    echo [INFO] Using g++ compiler...
    g++ -o test_compile.exe test_compile.cpp -lpsapi
    if %errorlevel% neq 0 (
        echo [ERROR] Basic compilation failed with g++
        pause
        exit /b 1
    )
) else (
    echo [INFO] Using MSVC compiler...
    cl /EHsc test_compile.cpp /Fe:test_compile.exe /link psapi.lib >nul 2>nul
    if %errorlevel% neq 0 (
        echo [ERROR] Basic compilation failed with MSVC
        pause
        exit /b 1
    )
)

echo [SUCCESS] Basic compilation test passed!
echo.

:: Test execution
echo [TEST 2] Testing compiled executable...
test_compile.exe
if %errorlevel% neq 0 (
    echo [ERROR] Compiled executable failed to run
    pause
    exit /b 1
)

echo.
echo [TEST 3] Testing MSBuild availability...
where msbuild >nul 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] MSBuild not found in PATH
    echo [INFO] Searching for MSBuild in common locations...
    
    set "MSBUILD_PATH="
    
    :: Check Visual Studio 2022
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    )
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    )
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    )
    
    :: Check Visual Studio 2019
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    )
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    )
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    )
    
    :: Check Build Tools
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
    )
    
    if defined MSBUILD_PATH (
        echo [SUCCESS] Found MSBuild at: %MSBUILD_PATH%
        echo [INFO] You can use this path to build the project manually
    ) else (
        echo [ERROR] MSBuild not found!
        echo [INFO] Please install Visual Studio or Build Tools
        echo [INFO] Download from: https://visualstudio.microsoft.com/downloads/
    )
) else (
    echo [SUCCESS] MSBuild found in PATH
)

echo.
echo ========================================
echo           TEST RESULTS
echo ========================================
echo [✓] Basic C++ compilation: PASSED
echo [✓] Executable runtime: PASSED
if defined MSBUILD_PATH (
    echo [✓] MSBuild availability: FOUND
) else (
    where msbuild >nul 2>nul
    if %errorlevel% equ 0 (
        echo [✓] MSBuild availability: FOUND
    ) else (
        echo [!] MSBuild availability: NOT FOUND
    )
)
echo.
echo Your system is ready to compile the FiveM Dumper!
echo.
echo Next steps:
echo 1. Open OffsetsDumper.sln in Visual Studio, OR
echo 2. Run: build.bat (if MSBuild is in PATH), OR
echo 3. Use the MSBuild path shown above
echo.

:: Cleanup
if exist "test_compile.exe" del "test_compile.exe" >nul 2>nul
if exist "test_compile.obj" del "test_compile.obj" >nul 2>nul

echo Press any key to exit...
pause >nul
