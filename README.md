# FiveM Advanced Dumper v2.0

Um dumper profissional e avançado para análise de memória do FiveM, desenvolvido com técnicas de engenharia reversa de alto nível.

## 🚀 Características

### Análise Avançada de Memória
- **Pattern Scanning (AOB)**: Busca avançada por padrões de bytes na memória
- **Análise de VTables**: Reconstrução automática de classes e estruturas virtuais
- **Cross-Reference Analysis**: Mapeamento de referências cruzadas entre funções
- **Static Analysis**: Análise estática de PE headers e seções
- **Import Table Analysis**: Análise detalhada da tabela de importações

### Técnicas Anti-Detecção
- **PEB Manipulation**: Ocultação do módulo na Process Environment Block
- **Anti-Debug Bypass**: Contorno de técnicas anti-debug
- **Memory Access Masking**: Mascaramento de acessos à memória
- **Stealth Injection**: Injeção furtiva sem detecção

### Funcionalidades Específicas do FiveM
- **Entity System Analysis**: Análise completa do sistema de entidades
- **Script Engine Dumping**: Extração de informações do motor de scripts
- **Network Structure Discovery**: Descoberta de estruturas de rede
- **Rendering System Analysis**: Análise do sistema de renderização
- **Pool Analysis**: Análise de pools de entidades (Peds, Vehicles, Objects)

### Outputs Profissionais
- **Offset Files**: Arquivos com todos os offsets encontrados
- **Function Signatures**: Assinaturas de funções identificadas
- **Class Reconstructions**: Reconstrução completa de classes
- **Header Generation**: Geração automática de headers C++
- **Cheat Engine Tables**: Tabelas prontas para Cheat Engine

## 📋 Requisitos

- **Visual Studio 2019/2022** com C++ Build Tools
- **Windows SDK** (versão 10.0 ou superior)
- **FiveM** em execução
- **Privilégios de Administrador** (recomendado)

## 🔧 Compilação

### Método 1: Visual Studio IDE

1. Abra o arquivo `OffsetsDumper.sln` no Visual Studio
2. Selecione a configuração **Release** e plataforma **x64**
3. Compile o projeto (Build → Build Solution)
4. O arquivo `OffsetsDumper.dll` será gerado em `OffsetsDumper\x64\Release\`

### Método 2: Command Line (MSBuild)

```bash
# Navegue até o diretório do projeto
cd "OffsetsDumper"

# Compile usando MSBuild
msbuild OffsetsDumper.sln /p:Configuration=Release /p:Platform=x64
```

### Método 3: Compilar o Injetor

```bash
# Compile o injetor separadamente
g++ -o FiveMInjector.exe FiveMInjector.cpp -lpsapi -static-libgcc -static-libstdc++
```

## 🎯 Uso

### Método Automático (Recomendado)

1. **Execute o FiveM** e entre em qualquer servidor
2. **Execute o injetor** como Administrador:
   ```bash
   FiveMInjector.exe
   ```
3. O injetor irá:
   - Detectar automaticamente o processo do FiveM
   - Injetar a DLL do dumper
   - Iniciar a análise automaticamente

### Método Manual

1. **Compile a DLL** seguindo as instruções acima
2. **Use um injetor de DLL** de sua preferência (Process Hacker, Cheat Engine, etc.)
3. **Injete** `OffsetsDumper.dll` no processo do FiveM
4. **Aguarde** a conclusão da análise

## 📁 Estrutura de Saída

Após a execução, os resultados serão salvos na pasta `FiveMDumps/`:

```
FiveMDumps/
├── offsets.txt          # Offsets principais do FiveM
├── functions.txt        # Funções identificadas
├── classes.txt          # Classes reconstruídas
├── FiveMOffsets.h       # Header C++ gerado
├── FiveM.CT            # Tabela do Cheat Engine
└── log.txt             # Log detalhado da análise
```

### Exemplo de Offsets Encontrados

```cpp
namespace FiveMOffsets {
    constexpr uintptr_t WorldPtr = 0x2485A28;
    constexpr uintptr_t EntityPool = 0x1F8B3A8;
    constexpr uintptr_t PedPool = 0x1F8B3B0;
    constexpr uintptr_t VehiclePool = 0x1F8B3B8;
    constexpr uintptr_t ObjectPool = 0x1F8B3C0;
    constexpr uintptr_t ScriptThreads = 0x1F8B3C8;
    constexpr uintptr_t NetworkManager = 0x1F8B3D0;
    constexpr uintptr_t Renderer = 0x1F8B3D8;
    constexpr uintptr_t Camera = 0x1F8B3E0;
}
```

## 🛡️ Recursos de Segurança

### Anti-Detecção Implementada
- **PEB Unlinking**: Remove o módulo da lista de módulos carregados
- **Debug Flag Clearing**: Limpa flags de debug no PEB
- **Memory Protection**: Proteção contra análise de memória
- **Stealth Threading**: Execução em threads separadas

### Técnicas Avançadas Utilizadas
- **Inline Hooking**: Para interceptação de funções
- **VMT Hooking**: Manipulação de tabelas virtuais
- **Pattern Scanning**: Busca robusta por padrões
- **Dynamic Analysis**: Análise dinâmica em tempo real
- **Static Disassembly**: Desmontagem estática de código

## ⚠️ Avisos Importantes

1. **Uso Educacional**: Este tool é destinado para pesquisa e educação em segurança
2. **Responsabilidade**: Use apenas em ambientes controlados e com permissão
3. **Anti-Cheat**: Pode ser detectado por sistemas anti-cheat avançados
4. **Atualizações**: Offsets podem mudar com atualizações do FiveM

## 🔍 Troubleshooting

### Problemas Comuns

**"Failed to open process"**
- Execute como Administrador
- Verifique se o FiveM está rodando
- Desative temporariamente o antivírus

**"DLL injection failed"**
- Verifique se a DLL foi compilada corretamente
- Certifique-se de usar a versão x64
- Verifique permissões de arquivo

**"No patterns found"**
- O FiveM pode ter sido atualizado
- Verifique se está usando a versão correta
- Aguarde o processo carregar completamente

### Debug Mode

Para ativar logs detalhados, modifique o arquivo `FiveMDumper.cpp`:

```cpp
#define DEBUG_MODE 1  // Adicione no topo do arquivo
```

## 📝 Changelog

### v2.0 (Atual)
- ✅ Análise avançada de VTables
- ✅ Reconstrução automática de classes
- ✅ Geração de headers C++
- ✅ Suporte a Cheat Engine
- ✅ Anti-detecção melhorada
- ✅ Pattern scanning otimizado

### v1.0
- ✅ Funcionalidade básica de dumping
- ✅ Detecção de offsets principais
- ✅ Análise de memória básica

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor:

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto é destinado apenas para fins educacionais e de pesquisa em segurança. O uso inadequado é de responsabilidade do usuário.

## 🔗 Links Úteis

- [FiveM Documentation](https://docs.fivem.net/)
- [Reverse Engineering Resources](https://github.com/wtsxDev/reverse-engineering)
- [x64dbg](https://x64dbg.com/)
- [Cheat Engine](https://cheatengine.org/)

---

**Desenvolvido por Advanced Security Research Team**
**Para uso educacional e pesquisa em segurança**
